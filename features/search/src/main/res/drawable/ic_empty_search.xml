<vector xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="107dp" android:viewportHeight="107" android:viewportWidth="106" android:width="106dp">
      
    <path android:fillColor="#F2F5FA" android:pathData="M76.93,41.04C76.94,21.2 60.86,5.12 41.02,5.14C21.33,5.12 5.33,21 5.14,40.66L5.13,40.67V41.03C5.13,56.62 15.06,69.9 28.97,74.86C32.72,76.2 36.8,76.94 41.03,76.93C41.15,76.94 41.28,76.94 41.39,76.94L41.41,76.92C61.06,76.73 76.94,60.74 76.93,41.04Z"/>
      
    <path android:fillColor="#D1D7E0" android:pathData="M28.43,52.86L28.43,52.86C28.07,53.55 27.22,53.83 26.52,53.48C25.82,53.13 25.54,52.28 25.89,51.58C26.05,51.26 30.25,43.1 40.62,43.1C51,43.1 55.19,51.25 55.36,51.58C55.71,52.28 55.42,53.14 54.72,53.49C54.52,53.59 54.3,53.64 54.09,53.64H54.08C53.56,53.64 53.06,53.35 52.81,52.85L52.81,52.85C52.65,52.55 49.17,45.95 40.62,45.95C32.08,45.95 28.59,52.55 28.43,52.86ZM40.62,70.95C23.93,70.95 10.36,57.37 10.36,40.68C10.36,23.99 23.93,10.41 40.62,10.41C57.31,10.41 70.89,23.99 70.89,40.68C70.89,57.37 57.31,70.95 40.62,70.95ZM40.62,13.26C25.5,13.26 13.2,25.56 13.2,40.68C13.2,55.8 25.5,68.1 40.62,68.1C55.74,68.1 68.04,55.8 68.04,40.68C68.04,25.56 55.74,13.26 40.62,13.26ZM50.24,36.33C48.39,36.33 46.89,34.83 46.89,32.99C46.89,31.14 48.39,29.64 50.24,29.64C52.08,29.64 53.58,31.14 53.58,32.99C53.58,34.83 52.08,36.33 50.24,36.33ZM31.01,36.33C29.16,36.33 27.66,34.83 27.66,32.99C27.66,31.14 29.16,29.64 31.01,29.64C32.85,29.64 34.35,31.14 34.35,32.99C34.35,34.83 32.85,36.33 31.01,36.33Z" android:strokeColor="#F2F5FA" android:strokeWidth="1"/>
      
    <path android:fillColor="#CCD1D9" android:pathData="M76.95,81.79L67.51,72.35C68.38,71.63 69.22,70.86 70.04,70.04C70.86,69.24 71.63,68.4 72.35,67.53L81.77,76.95L76.95,81.79Z"/>
      
    <path android:fillColor="#656D78" android:pathData="M81.79,76.95L84.2,74.52L104,94.34C106.67,97.01 106.67,101.33 104,104C101.33,106.68 97.01,106.68 94.32,104L74.52,84.2L76.95,81.79L81.77,76.95L81.79,76.95Z"/>
      
    <path android:fillColor="#434A54" android:pathData="M81.79,76.95L81.77,76.95L80.57,78.15L100.37,97.97C102.39,100 102.87,102.98 101.82,105.47C102.61,105.13 103.35,104.65 104,104C106.67,101.33 106.67,97.01 104,94.34L84.2,74.52L81.79,76.95Z"/>
      
    <path android:fillColor="#E1E6ED" android:pathData="M71.97,67.21L71.97,67.21C71.26,68.07 70.5,68.89 69.69,69.68L69.69,69.69C68.88,70.5 68.05,71.26 67.2,71.97L67.19,71.97L67.19,71.97C51.28,85.5 27.38,84.72 12.37,69.69L12.37,69.69C-3.46,53.87 -3.46,28.21 12.37,12.37C28.19,-3.46 53.86,-3.46 69.69,12.37C84.7,27.4 85.48,51.28 71.97,67.21ZM14.88,66.35V66.37L15.03,66.52L15.03,66.52C15.12,66.6 15.2,66.69 15.29,66.77C26.47,77.95 43.11,80.34 56.63,73.93C60.29,72.2 63.74,69.8 66.77,66.77C66.86,66.69 66.95,66.6 67.03,66.51L67.18,66.37V66.35C80.99,52.11 80.85,29.37 66.77,15.3C52.56,1.07 29.5,1.07 15.29,15.3C1.21,29.37 1.07,52.11 14.88,66.35Z" android:strokeColor="#ffffff" android:strokeWidth="1"/>
    
</vector>
