package com.builderkit.features.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.builderkit.base.ui.errors.UIError
import com.builderkit.base.ui.model.Product
import com.builderkit.features.search.commands.SearchCommandReceiver
import com.builderkit.repos.contracts.ProductsRepoContract
import com.builderkit.repos.contracts.SearchRepoContract
import com.builderkit.repos.contracts.StatisticsRepoContract
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class SearchViewModel(
    private val searchRepo: SearchRepoContract,
    private val statisticsRepo: StatisticsRepoContract,
    private val productRepo: ProductsRepoContract,
) : ViewModel(), SearchCommandReceiver {

    private val _searchQuery = MutableStateFlow("")
    val searchQuery = _searchQuery.asStateFlow()

    private val _state: MutableStateFlow<State> = MutableStateFlow(Success(loading = true))
    val state: StateFlow<State> = _state.asStateFlow()

    private val retryEvent = MutableSharedFlow<Unit>()

    init {
        loadStatistics()
        startSearchDebounce()
    }

    private fun loadStatistics() {
        viewModelScope.launch {
            statisticsRepo.getStatistics()
                .map {
                    it.fold(onSuccess = { result ->
                        Success(
                            loading = false,
                            statisticsResult = result.toStatisticsResult()
                        )
                    }, onFailure = {
                        Error(UIError.GenericError)
                    })
                }.collect { newState ->
                    _state.update {
                        newState
                    }
                }
        }
    }

    fun onSearchQueryChanged(query: String) {
        _searchQuery.update { query }
        if (query.isBlank())
            _state.update {
                (it as? Success)?.copy(searchResult = null) ?: it
            }
        else
            _state.update {
                (it as? Success)?.copy(loading = true) ?: it
            }
    }

    override fun retrySearch() {
        viewModelScope.launch {
            retryEvent.emit(Unit)
        }
    }

    override fun toggleFavorite(product: Product) {
        updateStateWithFavoriteProduct(product)
        viewModelScope.launch {
            productRepo.toggleFavorite(product.sku, isFavorite = product.isFavorite)
                .collect {
                    if (it.isFailure) {
                        updateStateWithFavoriteProduct(
                            (product as com.builderkit.mapper.models.product.Product).copy(
                                isFavorite = false
                            )
                        )
                    }
                }
        }
    }

    private fun updateStateWithFavoriteProduct(product: Product) {
        (_state.value as? Success)?.let { successState ->
            val statisticsResult = successState.statisticsResult ?: return
            val topProducts =
                statisticsResult.topProductsSearches.toMutableList() as MutableList<com.builderkit.mapper.models.product.Product>
            val appTopProducts =
                statisticsResult.appTopProductsSearches.toMutableList() as MutableList<com.builderkit.mapper.models.product.Product>

            val updateProductFavorite =
                { products: MutableList<com.builderkit.mapper.models.product.Product>, sku: String ->
                    products.indexOfFirst { it.sku == sku }.takeIf { it >= 0 }?.let { index ->
                        products[index] =
                            products[index].copy(isFavorite = !products[index].isFavorite)
                    }
                }

            updateProductFavorite(topProducts, product.sku)
            updateProductFavorite(appTopProducts, product.sku)

            _state.update {
                successState.copy(
                    statisticsResult = statisticsResult.copy(
                        topProductsSearches = topProducts.toImmutableList(),
                        appTopProductsSearches = appTopProducts.toImmutableList()
                    )
                )
            }
        }
    }

    private fun startSearchDebounce() {
        viewModelScope.launch {
            combine(
                searchQuery.debounce(500).distinctUntilChanged(),
                retryEvent.onStart { emit(Unit) },
            ) { query, _ ->
                query
            }.flatMapLatest { query ->
                if (query.isBlank()) {
                    emptyFlow()
                } else {
                    searchRepo.searchBy(query)
                }
            }.map { searchResult ->
                searchResult.fold(
                    onSuccess = {
                        (_state.value as? Success)?.copy(
                            loading = false,
                            searchResult = it.toSearchResult()
                        ) ?: Success(loading = false, searchResult = it.toSearchResult())
                    }, onFailure = {
                        (_state.value as? Success)?.copy(loading = false)
                            ?: Error(UIError.GenericError)
                    }
                )
            }.collect { newState ->
                _state.update {
                    newState
                }
            }
        }
    }
}