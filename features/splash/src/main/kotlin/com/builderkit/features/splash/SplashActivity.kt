package com.builderkit.features.splash

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.core.net.toUri
import androidx.core.view.WindowCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.builderkit.base.BuildConfig
import com.builderkit.base.ui.sheets.ConfirmationBottomSheet
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.updateLocale
import com.builderkit.features.splash.components.ForceUpdateBottomSheetComponent
import com.builderkit.features.splash.screen.SplashScreen
import com.builderkit.navigation.Navigator
import org.koin.android.ext.android.inject
import org.koin.android.scope.AndroidScopeComponent
import org.koin.androidx.scope.activityScope
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.qualifier.named
import org.koin.core.scope.Scope
import com.builderkit.data.localdatasource.pref.AppPreferences

class SplashActivity : ComponentActivity(), AndroidScopeComponent {

    override val scope: Scope by activityScope()

    private val viewmodel: SplashViewModel by viewModel()
    private val localePref by inject<AppPreferences.AppLanguage>()

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // MAKE ACTIVITY TO BE FULL SCREEN
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            val splashState by viewmodel.state.collectAsStateWithLifecycle()
            val onboardingNavigator: Navigator by scope.inject(named(ONBOARDING_NAVIGATOR))
            val mainNavigator: Navigator by scope.inject(named(MAIN_NAVIGATOR))

            LaunchedEffect(splashState) {
                if (splashState is SplashState.ConfigurationsLoaded) {
                    val shouldOpenLocationActivity = viewmodel.shouldOpenLocationActivity()
                    val loaded =
                        (splashState as SplashState.ConfigurationsLoaded).isConfigurationsLoaded
                    when {
                        loaded.not() && shouldOpenLocationActivity -> {
                            onboardingNavigator.navigateToFeature(this@SplashActivity)
                        }

                        loaded -> {
                            mainNavigator.navigateToFeature(this@SplashActivity)
                        }

                        else -> {
                            onboardingNavigator.navigateToFeature(this@SplashActivity)
                        }
                    }
                }
            }
            BuilderKitTheme(fullScreen = true) {
                Scaffold(
                    // ADD SUPPORT FOR FULL SCREEN
                    contentWindowInsets = WindowInsets(0, 0, 0, 0),
                ) { innerPadding ->
                    SplashScreen(modifier = Modifier.padding(innerPadding))

                    when (splashState) {
                        is SplashState.Loading,
                        is SplashState.ConfigurationsLoaded -> Unit

                        is SplashState.HasUpdate -> {
                            ForceUpdateBottomSheetComponent(onUpdateNowClicked = ::navigateToUpdateTheApp)
                        }

                        is SplashState.AppUpdateError -> {
                            ConfirmationBottomSheet(
                                title = stringResource(R.string.error_txt),
                                message = stringResource(R.string.something_went_wrong),
                                actionText = stringResource(R.string.try_again),
                                onConfirm = viewmodel::retryAppUpdates,
                                sheetState = rememberModalBottomSheetState(confirmValueChange = { false }),
                            )
                        }

                        is SplashState.NoServices -> {
                            ConfirmationBottomSheet(
                                title = stringResource(R.string.no_services_available),
                                message = stringResource(R.string.no_services_message),
                                actionText = stringResource(R.string.try_again),
                                onConfirm = viewmodel::retryAppServices,
                                sheetState = rememberModalBottomSheetState(confirmValueChange = { false }),
                            )
                        }

                        is SplashState.NoServicesError -> {
                            ConfirmationBottomSheet(
                                title = stringResource(R.string.error_txt),
                                message = stringResource(R.string.something_went_wrong),
                                actionText = stringResource(R.string.try_again),
                                onConfirm = viewmodel::retryAppServices,
                                sheetState = rememberModalBottomSheetState(confirmValueChange = { false }),
                            )
                        }
                    }
                }
            }
        }
    }

    override fun attachBaseContext(newBase: Context) {
        val currentAppLanguage = localePref.getAppLocale()
        val newContext = updateLocale(newBase, currentAppLanguage.value)
        super.attachBaseContext(newContext)
    }

    private fun navigateToUpdateTheApp() {
        Intent(Intent.ACTION_VIEW).apply {
            data =
                "https://play.google.com/store/apps/details?id=${BuildConfig.applicationId}".toUri()
            startActivity(this)
        }
    }

    companion object {
        const val ONBOARDING_NAVIGATOR = "onboarding-navigator"
        const val MAIN_NAVIGATOR = "main-navigator"
        const val LOCATION_NAVIGATOR = "location-navigator"

        @JvmStatic
        fun start(context: Context) {
            val starter = Intent(context, SplashActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            context.startActivity(starter)
        }
    }
}
