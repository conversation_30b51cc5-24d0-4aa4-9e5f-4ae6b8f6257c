package com.builderkit.features.order.listing.mock

import app.instakit.api.data.models.order.Order
import app.instakit.api.data.responses.cart.FetchCartResponse
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

internal fun getDummyOrders(): ImmutableList<Order> {
    return persistentListOf(
        Order(
            id = 12,
            status = Order.OrderStatus.READY,
            items = emptyList(),
            paymentMethod = "cash",
            paymentStatus = "unpaid",
            prices = FetchCartResponse.Prices(
                subtotal = 10.0,
                total = 10.0,
                deliveryFees = 10.0,
                extraFees = 0.0,
                promoDiscount = 0.0,
                vatCost = 0.0,
                walletValue = 0.0,
            ),
            earnedPoints = 50,
            createdAt = "Tue 18 June, 2024",
        ),
        Order(
            id = 12,
            status = Order.OrderStatus.READY,
            items = emptyList(),
            paymentMethod = "cash",
            paymentStatus = "unpaid",
            prices = FetchCartResponse.Prices(
                subtotal = 10.0,
                total = 10.0,
                deliveryFees = 10.0,
                extraFees = 0.0,
                promoDiscount = 0.0,
                vatCost = 0.0,
                walletValue = 0.0,
            ),
            earnedPoints = 50,
            createdAt = "Tue 18 June, 2024",
        ),
    )
}