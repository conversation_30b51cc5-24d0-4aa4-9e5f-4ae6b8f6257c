package com.builderkit.features.order.rate.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.extensions.toDataList
import com.builderkit.base.ui.model.DataList
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.spacing
import com.builderkit.features.order.rate.R
import com.builderkit.features.order.rate.model.FeedbackSettingsUI
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RateTags(
    title: String,
    tags: DataList<FeedbackSettingsUI.FeedbackAnswerUI>,
    selectedIds: ImmutableList<Int>,
    onSelectionChanged: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.SemiBold,
                color = LocalColors.current.text
            ),
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            tags.items.forEach { tag ->
                RateTag(
                    text = tag.answer,
                    isSelected = selectedIds.contains(tag.id),
                    onSelectionChanged = {
                        onSelectionChanged(tag.id)
                    }
                )
            }
        }
    }
}

@Composable
private fun RateTag(
    text: String,
    isSelected: Boolean,
    onSelectionChanged: () -> Unit
) {
    val borderColor = if (isSelected) LocalColors.current.text else LocalColors.current.borderLine
    Surface(
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(1.dp, borderColor),
        color = if (isSelected) LocalColors.current.text else Color.White,
        onClick = onSelectionChanged
    ) {
        Text(
            text = text,
            color = if (isSelected) Color.White else LocalColors.current.body,
            modifier = Modifier.padding(MaterialTheme.spacing.xSmall)
        )
    }
}

@Preview
@Composable
private fun RateTagsPreview() {
    BuilderKitTheme {
        RateTags(
            title = stringResource(R.string.order_feedback_label),
            tags = listOf(
                FeedbackSettingsUI.FeedbackAnswerUI(1, "Tag 1"),
                FeedbackSettingsUI.FeedbackAnswerUI(2, "Tag 2"),
                FeedbackSettingsUI.FeedbackAnswerUI(3, "Tag 3"),
                FeedbackSettingsUI.FeedbackAnswerUI(4, "Tag 4"),
                FeedbackSettingsUI.FeedbackAnswerUI(5, "Tag 5"),
            ).toDataList(),
            selectedIds = listOf(1, 3).toImmutableList(),
            onSelectionChanged = {}
        )
    }
}