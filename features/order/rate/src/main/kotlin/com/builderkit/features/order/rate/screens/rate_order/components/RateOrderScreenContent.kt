package com.builderkit.features.order.rate.screens.rate_order.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import app.instakit.api.data.models.order.Order
import com.builderkit.base.model.RateOrderUI
import com.builderkit.base.model.rateOrderUIMock
import com.builderkit.base.ui.components.AppButton
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.spacing
import com.builderkit.features.order.rate.R
import com.builderkit.features.order.rate.components.RateFeedback
import com.builderkit.features.order.rate.components.RateTags
import com.builderkit.features.order.rate.model.FeedbackSettingsUI.FeedbackQuestionUI
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
internal fun RateOrderScreenContent(
    actionLoading: Boolean,
    rateOrderUI: RateOrderUI,
    question: FeedbackQuestionUI?,
    selectedTags: ImmutableList<Int>,
    rate: Int,
    feedback: String,
    onFeedbackChange: (String) -> Unit,
    onRateChanged: (Int) -> Unit,
    onSelectedTagChanged: (Int) -> Unit,
    onSubmitClick: () -> Unit,
) {
    val buttonResId =
        if (rateOrderUI.orderServiceType == Order.OrderServiceType.DELIVERY) R.string.continue_txt else R.string.submit
    Column(
        modifier =
        Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(
                vertical = MaterialTheme.spacing.large,
                horizontal = MaterialTheme.spacing.medium,
            )
            .clickable(enabled = !actionLoading, onClick = {}),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        RateOrderSection(
            items = rateOrderUI.items,
            image = rateOrderUI.images,
            itemsCount = rateOrderUI.itemsCount,
            total = rateOrderUI.total,
            rate = rate,
            onRateChanged = onRateChanged
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xLarge))
        RateFeedback(
            title = stringResource(R.string.order_feedback_label),
            hint = stringResource(R.string.order_feedback_hint),
            feedback = feedback,
            onFeedbackChange = onFeedbackChange,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xLarge))
        question?.let {
            RateTags(
                title = it.question,
                tags = it.answers,
                selectedIds = selectedTags,
                onSelectionChanged = onSelectedTagChanged
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xLarge))
        }
        AppButton(
            contentText = stringResource(buttonResId),
            modifier = Modifier.fillMaxWidth(),
            enabled = rate > 0,
            loading = actionLoading,
            onClick = onSubmitClick,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun RateOrderScreenContentPreview() {
    BuilderKitTheme {
        RateOrderScreenContent(
            actionLoading = false,
            rateOrderUI = rateOrderUIMock,
            rate = 0,
            feedback = "",
            onRateChanged = {},
            onFeedbackChange = {},
            question = null,
            selectedTags = persistentListOf(1, 2),
            onSelectedTagChanged = {},
            onSubmitClick = {},
        )
    }
}
