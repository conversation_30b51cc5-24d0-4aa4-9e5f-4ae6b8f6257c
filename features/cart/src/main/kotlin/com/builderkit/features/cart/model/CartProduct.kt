package com.builderkit.features.cart.model

import com.builderkit.base.ui.model.Product

data class CartProduct(
    val id: Long,
    override val sku: String,
    override val name: String,
    override val imageUrl: String,
    override val finalPrice: String,
    override val originalPrice: String,
    val quantity: Int,
    override val attributes: List<Product.Attribute>? = null,
    override val description: String?,
    override val thumbnail: String?,
    override val media: List<Product.Media>,
    override val tags: List<String>,
    override val discountPercentage: String?,
    override val finalDisplayPrice: String,
    override val originalDisplayPrice: String,
    override val defaultAttributes: List<Product.Attribute>?,
    override val isAvailable: Boolean,
    override val shareLink: String? = null,
    override val notifyMeWhenAvailable: Boolean,
    override val isFavorite: Boolean,
) : Product

data class CartProductAttribute(
    override val id: Long,
    override val name: String,
    override val minSelections: Int,
    override val maxSelections: Int,
    override val index: Int,
    override val options: List<Product.Attribute.Option>,
    override val required: Boolean,
    override val description: String?,
    override val type: Product.Attribute.Type,
) : Product.Attribute

data class CartProductAttributeOption(
    override val id: Long,
    override val name: String,
    override val price: String,
    override val isSelected: Boolean,
    override val index: Int,
    override val value: String?,
    override val image: String?,
) : Product.Attribute.Option
