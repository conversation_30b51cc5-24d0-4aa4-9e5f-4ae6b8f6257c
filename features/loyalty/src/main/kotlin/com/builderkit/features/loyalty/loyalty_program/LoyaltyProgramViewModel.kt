package com.builderkit.features.loyalty.loyalty_program

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.instakit.api.data.models.user.User
import app.instakit.api.data.requests.loyalty.MyCouponsFilterType
import app.instakit.api.data.responses.loyalty.MyCoupon
import com.builderkit.base.dispatchers.DispatcherProvider
import com.builderkit.base.ui.model.SectionUiState
import com.builderkit.features.loyalty.loyalty_program.commands.LoyaltyProgramCommandsReceiver
import com.builderkit.features.loyalty.model.CouponUI
import com.builderkit.features.loyalty.model.UserTierUI
import com.builderkit.features.loyalty.model.toCouponUIList
import com.builderkit.features.loyalty.model.toHowItWorksUI
import com.builderkit.features.loyalty.model.toTiersUIList
import com.builderkit.repos.contracts.LoyaltyRepoContract
import com.builderkit.repos.contracts.UserRepoContract
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.launch

class LoyaltyProgramViewModel(
    private val userRepo: UserRepoContract,
    private val loyaltyRepo: LoyaltyRepoContract,
    private val dispatchers: DispatcherProvider
) : ViewModel(), LoyaltyProgramCommandsReceiver {
    private val _uiState: MutableStateFlow<LoyaltyProgramState> =
        MutableStateFlow(LoyaltyProgramState.Loading)
    val uiState: StateFlow<LoyaltyProgramState> = _uiState

    private val _sideEffect: Channel<LoyaltyProgramSideEffect> = Channel()
    val sideEffect: Flow<LoyaltyProgramSideEffect> = _sideEffect.receiveAsFlow()

    private var user: User? = null

    init {
        loadLoyaltyProgram()
    }

    private fun loadLoyaltyProgram() {
        viewModelScope.launch {
            userRepo.fetchUser()
                .onStart {
                    _uiState.update { LoyaltyProgramState.Loading }
                }
                .flowOn(dispatchers.io).collectLatest { result ->
                    user = result
                    if (result != null) {
                        loadUserLoyaltyProgram()
                    } else {
                        loadGuestLoyaltyProgram()
                    }
                }
        }
    }


    private fun loadGuestLoyaltyProgram() {
        viewModelScope.launch {
            loyaltyRepo.fetchTiers()
                .onStart {
                    _uiState.update { LoyaltyProgramState.Loading }
                }
                .flowOn(dispatchers.io).collect { result ->
                    val state = result.getOrNull()?.let {
                        LoyaltyProgramState.Success.Guest(
                            tiers = it.toTiersUIList(),
                        )
                    } ?: LoyaltyProgramState.Error
                    _uiState.update { state }
                }
        }
    }

    private fun loadUserLoyaltyProgram() {
        if (user != null && _uiState.value is LoyaltyProgramState.Success.User) {
            _uiState.update {
                val currentState = it as LoyaltyProgramState.Success.User
                currentState.copy(
                    userTierUI = UserTierUI(
                        tierName = user!!.currentTier.title,
                        tierImageUrl = user!!.currentTier.mainImage,
                        tierCaption = user!!.nextTierCaption,
                        validPoints = user!!.totalValidPoints,
                        equivalentValue = user!!.userValidMoney,
                        totalPoints = user!!.totalGainedPoints,
                        progress = (user!!.totalGainedPoints.toFloat() / user!!.currentTier.points)
                    ),
                    howItWorks = user!!.currentTier.howItWorks.toHowItWorksUI(),
                    inviteFriendMessage = user?.inviteFriend?.invitation?.title ?: "",
                    inviteFriendCaption = user?.inviteFriend?.invitation?.description ?: "",
                )
            }
            retryMyCoupons()
            return
        }
        viewModelScope.launch {
            loyaltyRepo.fetchAvailableCoupons().zip(
                loyaltyRepo.fetchMyCoupons(MyCouponsFilterType.VALID)
            ) { availableCoupons, myCoupons ->
                availableCoupons to myCoupons
            }.onStart {
                _uiState.update { LoyaltyProgramState.Loading }
            }.map { result ->
                val (availableCoupons, myCoupons) = result
                val user = <EMAIL>!!
                if (availableCoupons.getOrNull() != null && myCoupons.getOrNull() != null) {
                    LoyaltyProgramState.Success.User(
                        userTierUI = UserTierUI(
                            tierName = user.currentTier.title,
                            tierImageUrl = user.currentTier.mainImage,
                            tierCaption = user.nextTierCaption,
                            validPoints = user.totalValidPoints,
                            equivalentValue = user.userValidMoney,
                            totalPoints = user.totalGainedPoints,
                            progress = (user.totalGainedPoints.toFloat() / user.currentTier.points)
                        ),
                        howItWorks = user.currentTier.howItWorks.toHowItWorksUI(),
                        redeemableCoupons = availableCoupons.getOrNull()!!.toCouponUIList(),
                        myCouponsState = mapMyCouponsToSectionUiState(myCoupons),
                        inviteFriendMessage = user.inviteFriend?.invitation?.title ?: "",
                        inviteFriendCaption = user.inviteFriend?.invitation?.description ?: "",
                    )
                } else LoyaltyProgramState.Error
            }
                .collect { result ->
                    _uiState.update { result }
                }
        }
    }

    private fun retryMyCoupons() {
        viewModelScope.launch {
            loyaltyRepo.fetchMyCoupons(MyCouponsFilterType.VALID)
                .onStart {
                    val currentState = _uiState.value as? LoyaltyProgramState.Success.User
                    currentState?.let {
                        _uiState.update {
                            currentState.copy(myCouponsState = SectionUiState.Loading)
                        }
                    }
                }.map {
                    mapMyCouponsToSectionUiState(it)
                }
                .collect { result ->
                    val currentState = _uiState.value as? LoyaltyProgramState.Success.User
                    currentState?.let {
                        _uiState.update {
                            currentState.copy(myCouponsState = result)
                        }
                    }

                }
        }
    }

    private fun mapMyCouponsToSectionUiState(coupons: Result<List<MyCoupon>>): SectionUiState<CouponUI> {
        return coupons.fold(
            onSuccess = {
                if (it.isEmpty()) SectionUiState.Empty else SectionUiState.Success(it.toCouponUIList())
            },
            onFailure = {
                SectionUiState.Error
            }
        )
    }

    override fun onRetryClick() {
        if (user != null) {
            loadUserLoyaltyProgram()
        } else {
            loadGuestLoyaltyProgram()
        }
    }

    override fun onRetryMyCouponsClick() {
        retryMyCoupons()
    }
}