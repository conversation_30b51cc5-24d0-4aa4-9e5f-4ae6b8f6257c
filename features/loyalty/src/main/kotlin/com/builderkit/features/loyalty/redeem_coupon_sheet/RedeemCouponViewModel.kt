package com.builderkit.features.loyalty.redeem_coupon_sheet

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.builderkit.base.dispatchers.DispatcherProvider
import com.builderkit.repos.contracts.LoyaltyRepoContract
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class RedeemCouponViewModel(
    private val loyaltyRepo: LoyaltyRepoContract,
    private val dispatchers: DispatcherProvider
) : ViewModel() {
    private val _uiState: MutableStateFlow<RedeemCouponState> =
        MutableStateFlow(RedeemCouponState.Idle)
    val uiState: StateFlow<RedeemCouponState> = _uiState

    private var redeemCouponJob: Job? = null

    fun redeemCoupon(couponId: Long) {
        redeemCouponJob?.cancel()
        redeemCouponJob = viewModelScope.launch {
            loyaltyRepo.redeemCoupon(couponId)
                .onStart { _uiState.update { RedeemCouponState.Loading } }
                .flowOn(dispatchers.io)
                .collect { result ->
                    result.fold(
                        onSuccess = { _uiState.update { RedeemCouponState.Success } },
                        onFailure = { throwable ->
                            _uiState.update { RedeemCouponState.Error(throwable.message) }
                        }
                    )
                }
        }
    }

    fun reset() {
        redeemCouponJob?.cancel()
        _uiState.update { RedeemCouponState.Idle }
    }
}