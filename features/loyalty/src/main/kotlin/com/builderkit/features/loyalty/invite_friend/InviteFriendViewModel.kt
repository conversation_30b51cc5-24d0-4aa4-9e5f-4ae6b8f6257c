package com.builderkit.features.loyalty.invite_friend

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.builderkit.base.dispatchers.DispatcherProvider
import com.builderkit.features.loyalty.invite_friend.model.toInviteFriendUI
import com.builderkit.repos.contracts.UserRepoContract
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class InviteFriendViewModel(
    private val userRepo: UserRepoContract,
    private val dispatcherProvider: DispatcherProvider
) : ViewModel() {
    private val _uiState: MutableStateFlow<InviteFriendState> =
        MutableStateFlow(InviteFriendState.Loading)
    val uiState: StateFlow<InviteFriendState> = _uiState

    private val _sideEffect: Channel<InviteFriendSideEffect> = Channel()
    val sideEffect: Flow<InviteFriendSideEffect> = _sideEffect.receiveAsFlow()

    init {
        fetchInviteFriendData()
    }

    private fun fetchInviteFriendData() {
        viewModelScope.launch {
            userRepo.fetchUser()
                .onStart {
                    _uiState.update { InviteFriendState.Loading }
                }
                .flowOn(dispatcherProvider.io).collectLatest { result ->
                    _uiState.update {
                        if (result != null) {
                            InviteFriendState.Success(
                                inviteFriendUI = result.toInviteFriendUI()
                            )
                        } else {
                            InviteFriendState.Error
                        }
                    }
                }
        }
    }
}