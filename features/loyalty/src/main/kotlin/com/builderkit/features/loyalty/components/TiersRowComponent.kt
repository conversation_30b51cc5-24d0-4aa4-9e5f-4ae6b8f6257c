package com.builderkit.features.loyalty.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.EqualHeightLazyRow
import com.builderkit.base.ui.extensions.AsyncImage
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.spacing
import com.builderkit.features.loyalty.R
import com.builderkit.features.loyalty.model.TierUI
import com.builderkit.features.loyalty.model.TierUIMockList
import kotlinx.collections.immutable.ImmutableList

@Composable
fun TiersRowComponent(
    tiers: ImmutableList<TierUI>,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            modifier = Modifier.padding(start = MaterialTheme.spacing.large),
            text = stringResource(id = R.string.tiers),
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight(600),
                color = LocalColors.current.text,
            )
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        EqualHeightLazyRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            contentPadding = PaddingValues(horizontal = MaterialTheme.spacing.large),
            items = tiers,
            content = { tier, height ->
                TierItem(
                    modifier = Modifier.heightIn(min = height),
                    tierUI = tier
                )
            }
        )
    }

}

@Composable
private fun TierItem(
    tierUI: TierUI,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AsyncImage(
            modifier = Modifier.sizeIn(
                maxWidth = 72.dp,
                maxHeight = 72.dp,
            ),
            url = tierUI.imageUri,
            contentDescription = tierUI.title,
            contentScale = ContentScale.Fit
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xSmall))
        Text(
            text = tierUI.title,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight(600),
                color = LocalColors.current.body,
            )
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxSmall))
        Text(
            text = stringResource(
                id = R.string.points_format,
                tierUI.points
            ),
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.Normal,
                color = LocalColors.current.body,
            )
        )
    }
}


@Preview(showBackground = true)
@Composable
private fun TiersRowComponentPreview() {
    BuilderKitTheme {
        TiersRowComponent(
            modifier = Modifier.fillMaxSize(),
            tiers = TierUIMockList
        )
    }
}