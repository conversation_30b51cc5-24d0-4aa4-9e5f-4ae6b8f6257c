package com.builderkit.features.loyalty.components.coupon_component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.theme.BuilderKitTheme

@Composable
fun CouponSurface(
    description: @Composable () -> Unit,
    value: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    val cornerRadius = 20.dp
    val borderWidth = 1.dp
    val dashWidth = 4.dp
    val dashGap = 4.dp
    val brush = SolidColor(Color(0xFFD9D9D9))
    Row(modifier = modifier.height(intrinsicSize = IntrinsicSize.Max)) {
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1f)
                .dashedBorderExceptTrailingSide(
                    shape = RoundedCornerShape(cornerRadius),
                    strokeWidth = borderWidth,
                    dashLength = dashWidth,
                    gapLength = dashGap,
                    cornerRadius = cornerRadius,
                    brush = brush
                ),
        ) {
            description()
        }
        CouponSurfaceSeparatorComponent(
            radius = 15.dp,
            strokeWidth = borderWidth,
            dashLength = dashWidth,
            gapLength = dashGap,
            brush = brush
        )
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .dashedBorderExceptLeadingSide(
                    shape = RoundedCornerShape(cornerRadius),
                    strokeWidth = borderWidth,
                    dashLength = dashWidth,
                    gapLength = dashGap,
                    cornerRadius = cornerRadius,
                    brush = brush
                )
        ) {
            value()
        }
    }
}

@Composable
private fun CouponSurfaceSeparatorComponent(
    radius: Dp,
    strokeWidth: Dp,
    dashLength: Dp,
    gapLength: Dp,
    brush: Brush,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        HalfCircleWithDashedLineBorder(
            radius = radius,
            strokeWidth = strokeWidth,
            dashLength = dashLength,
            gapLength = gapLength,
            brush = brush,
            isInverted = true
        )
        VerticalDashedLine(
            modifier = Modifier
                .padding(8.dp)
                .weight(1f),
            dashLength = dashLength,
            gapLength = gapLength,
            brush = brush,
            strokeWidth = strokeWidth,
        )
        HalfCircleWithDashedLineBorder(
            radius = radius,
            strokeWidth = strokeWidth,
            dashLength = dashLength,
            gapLength = gapLength,
            brush = brush,
            isInverted = false
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun CouponSurfacePreview() {
    BuilderKitTheme {
        Column(modifier = Modifier.padding(10.dp)) {
            CouponSurface(
                description = {},
                value = {
                    Box(modifier = Modifier.size(100.dp))
                }
            )
            Spacer(modifier = Modifier.height(10.dp))
            CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Rtl) {
                CouponSurface(
                    description = {},
                    value = {
                        Box(modifier = Modifier.size(100.dp))
                    }
                )
            }
        }
    }
}



