package com.builderkit.features.loyalty.model

import app.instakit.api.data.responses.loyalty.HowItWorksResponse
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

data class HowItWorksUI(
    val title: String,
    val description: String,
    val imageUri: String,
)

fun List<HowItWorksResponse>.toHowItWorksUI(): ImmutableList<HowItWorksUI> {
    return map {
        HowItWorksUI(
            title = it.title,
            description = it.description,
            imageUri = it.icon,
        )
    }.toImmutableList()
}

internal val HowItWorksUIMockList = persistentListOf(
    HowItWorksUI(
        title = "Order to earn points",
        description = "Earn points with each order 1 point / 1 EGP",
        imageUri = "",
    ),
    HowItWorksUI(
        title = "Redeem exciting deals",
        description = "Use your points to redeem exciting deals",
        imageUri = "",
    ),
    HowItWorksUI(
        title = "Level up",
        description = "Earn enough points and level up to unlock exclusive benefits",
        imageUri = "",
    ),
)
