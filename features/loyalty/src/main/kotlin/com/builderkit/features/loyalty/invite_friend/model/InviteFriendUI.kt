package com.builderkit.features.loyalty.invite_friend.model

import app.instakit.api.data.models.user.User
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

data class InviteFriendUI(
    val headline: String,
    val invitationCode: String,
    val invitationMessage: String,
    val inviteFriendItems: ImmutableList<InviteFriendItem>,
)

fun User.toInviteFriendUI() = inviteFriend?.let {
    InviteFriendUI(
        headline = it.invitation.title,
        invitationCode = it.shareCode,
        invitationMessage = it.shareCodeMessage,
        inviteFriendItems = it.howItWorks.map { howItWorks ->
            InviteFriendItem(
                title = howItWorks.title,
                description = howItWorks.description,
                icon = howItWorks.icon
            )
        }.toImmutableList()
    )
}


internal val inviteFriendUIMock = InviteFriendUI(
    headline = "Invite a friend to get 20 points",
    invitationCode = "Instakit132",
    invitationMessage = "",
    inviteFriendItems = persistentListOf(
        InviteFriendItem(
            title = "Share the code",
            description = "Get discount vouchers worth 60 EGP valid on selected stores for each friend joins requests.",
            icon = ""
        ),
        InviteFriendItem(
            title = "Reward your friends",
            description = "your friend will get a discount of EGP 150 on the first 3 orders.",
            icon = ""
        ),
    )
)
