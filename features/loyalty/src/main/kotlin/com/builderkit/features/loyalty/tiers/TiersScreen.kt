package com.builderkit.features.loyalty.tiers

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.builderkit.base.ui.components.viewWithAction.GeneralViewType
import com.builderkit.base.ui.components.viewWithAction.ViewWithAction
import com.builderkit.features.loyalty.tiers.commands.RetryCommand
import com.builderkit.features.loyalty.tiers.commands.TiersCommand
import com.builderkit.features.loyalty.tiers.components.TiersDetailsContent
import com.builderkit.features.loyalty.tiers.components.TiersDetailsLoadingView

@Composable
fun TiersScreen(
    state: TiersState,
    onCommand: (command: TiersCommand) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {

        when (state) {
            is TiersState.Loading -> {
                TiersDetailsLoadingView()
            }

            is TiersState.Success -> {
                TiersDetailsContent(tiersList = state.tiersDetailsList)
            }

            is TiersState.Error -> {
                ViewWithAction(
                    viewType = GeneralViewType.Error,
                    modifier = Modifier.fillMaxSize(),
                    action = { onCommand(RetryCommand()) }
                )
            }
        }
    }
}