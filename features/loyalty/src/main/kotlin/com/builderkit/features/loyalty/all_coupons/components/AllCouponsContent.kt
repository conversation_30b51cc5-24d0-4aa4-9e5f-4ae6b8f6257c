package com.builderkit.features.loyalty.all_coupons.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import app.instakit.api.data.models.user.Invitation
import com.builderkit.base.ui.LocalSnackBarHostState
import com.builderkit.base.ui.components.viewWithAction.GeneralView
import com.builderkit.base.ui.components.viewWithAction.GeneralViewType
import com.builderkit.base.ui.components.viewWithAction.ViewWithAction
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.spacing
import com.builderkit.features.loyalty.R
import com.builderkit.features.loyalty.components.InviteFriendComponent
import com.builderkit.features.loyalty.components.coupon_component.CouponComponent
import com.builderkit.features.loyalty.model.CouponUI
import com.builderkit.features.loyalty.model.couponUIMock
import com.builderkit.features.loyalty.redeem_coupon_sheet.RedeemCouponSheet
import com.builderkit.features.loyalty.redeem_coupon_sheet.RedeemCouponViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AllCouponsContent(
    couponsList: ImmutableList<CouponUI>,
    invitation: Invitation?,
    onInviteFriendClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    val redeemCouponSheetState = rememberModalBottomSheetState()
    val coroutineScope = rememberCoroutineScope()
    val redeemCouponViewModel: RedeemCouponViewModel = koinViewModel()
    var redeemableCoupon by remember { mutableStateOf<CouponUI?>(null) }
    val snackbarHostState = LocalSnackBarHostState.current
    val context = LocalContext.current

    Column(modifier = modifier) {
        if (couponsList.isEmpty()) {
            ViewWithAction(
                viewType = GeneralViewType.Custom(
                    GeneralView(
                        imageRes = R.drawable.ic_empty_coupons,
                        title = stringResource(id = R.string.no_vouchers_available)
                    )
                ),
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f),
            )
        } else {
            LazyColumn(
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.large,
                        vertical = MaterialTheme.spacing.medium
                    )
                    .fillMaxSize()
                    .weight(1f),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large)
            ) {
                items(couponsList) {
                    CouponComponent(
                        couponUI = it,
                        onCopyCouponCodeClick = {},
                        onRedeemCouponClick = {
                            redeemCouponViewModel.reset()
                            redeemableCoupon = it
                            coroutineScope.launch {
                                redeemCouponSheetState.show()
                            }
                        }
                    )
                }
            }
        }

        invitation?.let {
            InviteFriendComponent(
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.large
                    )
                    .clickable(onClick = onInviteFriendClicked),
                message = it.title,
                caption = it.description
            )
        }
    }

    if (redeemableCoupon != null && redeemCouponSheetState.isVisible) {
        RedeemCouponSheet(
            couponUI = redeemableCoupon!!,
            sheetState = redeemCouponSheetState,
            redeemCouponViewModel = redeemCouponViewModel,
            onDismiss = {
                redeemableCoupon = null
                coroutineScope.launch {
                    redeemCouponSheetState.hide()
                }
            },
            onSuccessfulRedemption = {
                redeemableCoupon = null
                coroutineScope.launch {
                    redeemCouponSheetState.hide()
                    snackbarHostState.showSnackbar(
                        message = context.getString(R.string.coupon_redeemed_successfully)
                    )
                }
            }
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AllCouponsContentPreview() {
    BuilderKitTheme {
        AllCouponsContent(
            couponsList = persistentListOf(couponUIMock),
            invitation = Invitation(
                title = "Invite a friend to get 20 points",
                description = "Instakit132",
                icon = ""
            ),
            onInviteFriendClicked = {}
        )
    }
}