package com.builderkit.features.product.category_products

import androidx.compose.material3.SnackbarDuration
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import com.builderkit.base.R
import com.builderkit.base.pagination.PagingInfo
import com.builderkit.base.pagination.PagingResponse
import com.builderkit.base.pagination2.PagingData2
import com.builderkit.base.pagination2.PagingLoader2
import com.builderkit.base.ui.LocalSnackBarHostState
import com.builderkit.base.ui.LocalUserAvailableState
import com.builderkit.base.ui.components.product_card.ProductCard
import com.builderkit.base.ui.components.product_card.getProduct
import com.builderkit.base.ui.pagination2.PaginatedList
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.features.product.category_products.commands.CategoryProductsCommands
import com.builderkit.features.product.category_products.commands.ToggleFavoriteCommand
import com.builderkit.features.product.side_effect.LoadCategoryProductsSideEffect
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.onEach
import com.builderkit.features.product.states.ProductsByCategoryUIState as State

@Composable
fun CategoryProductsListingScreen(
    state: State,
    paginatedData: PagingData2<ProductCard>,
    sideEffect: Flow<LoadCategoryProductsSideEffect>,
    onCommand: (CategoryProductsCommands) -> Unit,
    onProductClick: (productSku: String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val snackbarHostState = LocalSnackBarHostState.current
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        sideEffect
            .onEach {
                when (it) {
                    is LoadCategoryProductsSideEffect.ShowError -> {
                        val message =
                            it.errorMessage ?: context.getString(R.string.something_went_wrong)
                        snackbarHostState.showSnackbar(
                            message = message,
                            duration = SnackbarDuration.Long,
                        )
                    }
                }
            }.collect()
    }

    var loading by remember { mutableStateOf(false) }
    var error by remember { mutableStateOf(false) }
    var isGuest by remember { mutableStateOf(true) }
    var products by remember { mutableStateOf<ImmutableList<ProductCard>?>(null) }

    PaginatedList(
        modifier = modifier,
        pagingData = paginatedData,
        withDivider = false,
        itemContent = { item ->
            ProductCard(
                productCard = item,
                isGuest = LocalUserAvailableState.current.not(),
                onClick = onProductClick,
                onFavoriteClick = {
                    onCommand(ToggleFavoriteCommand(item.sku, item.isFavorite))
                },
            )
        },
    )
//    when (state) {
//        is State.Loading -> {
//            loading = true
//            error = false
//        }
//
//        is State.Error -> {
//            loading = false
//            error = true
//        }
//
//        is State.ProductsByCategoryUIData -> {
//            products = state.products
//            loading = state.loading
//            isGuest = LocalUserAvailableState.current.not()
//            error = false
//        }
//    }
//    Box {
//        products?.let { products ->
//            LazyColumn(
//                modifier = modifier,
//                contentPadding = PaddingValues(20.dp),
//                verticalArrangement = Arrangement.spacedBy(12.dp),
//            ) {
//                items(
//                    items = products,
//                    key = { product -> product.sku },
//                ) { item: ProductCard ->
//                    ProductCard(
//                        productCard = item,
//                        isGuest = isGuest,
//                        onClick = onProductClick,
//                        onFavoriteClick = {
//                            onCommand(ToggleFavoriteCommand(item.sku, item.isFavorite))
//                        },
//                    )
//                }
//            }
//        }
//
//        if (error) {
//            ViewWithAction(
//                viewType = GeneralViewType.Error,
//                modifier = modifier.fillMaxSize(),
//                action = {
//                    onCommand(LoadCategoryProductsCommand())
//                }
//            )
//        }
//
//        if (loading) {
//            FullScreenLoading()
//        }
//    }

}

@Preview(showBackground = true)
@Composable
private fun CategoryProductsListingPreview() {
    BuilderKitTheme {
        val coroutineScope = rememberCoroutineScope()
        val response = object : PagingResponse<ProductCard> {
            override val items: List<ProductCard>
                get() = listOf(getProduct(), getProduct())
            override val pagingInfo: PagingInfo
                get() = PagingInfo(totalItems = 2, lastPage = 1)

        }
        val loader = object : PagingLoader2<ProductCard> {
            override fun loadNextPage(page: Int): Flow<Result<PagingResponse<ProductCard>>> =
                flowOf(Result.success(response))
        }
        val paginatedData = PagingData2(
            coroutineScope = coroutineScope,
            pagingLoader = loader,
        )
        CategoryProductsListingScreen(
            state = State.ProductsByCategoryUIData(
                persistentListOf(
                    getProduct(),
                    getProduct().copy(sku = "2"),
                    getProduct().copy(sku = "3"),
                ),
            ),
            onCommand = {},
            onProductClick = {},
            sideEffect = flow {},
            paginatedData = paginatedData,
        )
    }
}
