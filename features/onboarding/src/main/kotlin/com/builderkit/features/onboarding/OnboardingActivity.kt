package com.builderkit.features.onboarding

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import com.builderkit.base.ui.model.DataList
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.features.onboarding.model.OnboardingData
import com.builderkit.features.onboarding.navigation.OnboardingDestination
import com.builderkit.features.onboarding.navigation.OnboardingStartDestination
import com.builderkit.features.onboarding.navigation.OnboardingStepsDestination
import com.builderkit.features.onboarding.screen.OnboardingScreen
import com.builderkit.features.onboarding.screen.OnboardingStepScreen
import com.builderkit.navigation.Navigator
import kotlinx.coroutines.launch
import org.koin.android.scope.AndroidScopeComponent
import org.koin.androidx.scope.activityScope
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.qualifier.named
import org.koin.core.scope.Scope

class OnboardingActivity : ComponentActivity(), AndroidScopeComponent {

    override val scope: Scope by activityScope()
    private val locationNavigator: Navigator by scope.inject(named(LOCATION_NAVIGATOR))
    private val mainNavigator: Navigator by scope.inject(named(MAIN_NAVIGATOR))
    private val viewmodel: OnboardingViewModel by viewModel()

    private val stepsData by lazy {
        listOf(
            OnboardingData(
                image = R.drawable.onboarding_step1_image,
                imageDescription = R.string.onboarding_step1_image_description,
                title = R.string.onboarding_step1_title,
                description = R.string.onboarding_step1_description,
            ),
            OnboardingData(
                image = R.drawable.onboarding_step1_image,
                imageDescription = R.string.onboarding_step1_image_description,
                title = R.string.onboarding_step1_title,
                description = R.string.onboarding_step1_description,
            ),
            OnboardingData(
                image = R.drawable.onboarding_step1_image,
                imageDescription = R.string.onboarding_step1_image_description,
                title = R.string.onboarding_step1_title,
                description = R.string.onboarding_step1_description,
            ),
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // to apply full screen
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            BuilderKitTheme(fullScreen = true) {
                var currentScreen: OnboardingDestination by remember {
                    mutableStateOf(OnboardingStartDestination)
                }
                val coroutineScope = rememberCoroutineScope()
                Scaffold(
                    // APPLY FULL SCREEN
                    contentWindowInsets = WindowInsets(0, 0, 0, 0),
                ) { paddings ->
                    if (currentScreen == OnboardingStartDestination) {
                        OnboardingScreen(modifier = Modifier.padding(paddings)) {
                            coroutineScope.launch {
                                if (viewmodel.shouldOpenLocationActivity()) {
                                    locationNavigator.navigateToFeature(this@OnboardingActivity)
                                } else {
                                    mainNavigator.navigateToFeature(this@OnboardingActivity)
                                }
                            }
                        }
                    }
                    AnimatedVisibility(
                        visible = currentScreen == OnboardingStepsDestination,
                        enter = slideInHorizontally { it },
                        exit = slideOutHorizontally(),
                    ) {
                        OnboardingStepScreen(
                            content = DataList(stepsData),
                            pagesCount = stepsData.size,
                            modifier = Modifier.padding(paddings),
                        ) {
                            coroutineScope.launch {
                                if (viewmodel.shouldOpenLocationActivity()) {
                                    locationNavigator.navigateToFeature(this@OnboardingActivity)
                                } else {
                                    mainNavigator.navigateToFeature(this@OnboardingActivity)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    companion object {
        const val LOCATION_NAVIGATOR = "location-navigator"
        const val MAIN_NAVIGATOR = "main-navigator"
    }
}
