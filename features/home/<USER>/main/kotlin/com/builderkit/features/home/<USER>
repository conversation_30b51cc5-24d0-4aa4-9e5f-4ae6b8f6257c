package com.builderkit.features.home

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.components.cards.product.VerticalProductCard
import com.builderkit.base.ui.model.Product
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun ProductsRow(
    data: ImmutableList<Product>,
    horizontalPadding: Int,
    contentPadding: Int,
    onFavoriteClick: (sku: String, isFavorite: Boolean) -> Unit,
    modifier: Modifier = Modifier,
    onProductClicked: (product: Product) -> Unit = {},
) {
    LazyRow(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        contentPadding = PaddingValues(horizontal = horizontalPadding.dp),
        horizontalArrangement = Arrangement.spacedBy(contentPadding.dp),
    ) {
        items(data) { item ->
            VerticalProductCard(
                modifier = Modifier
                    .requiredWidth(162.dp)
                    .clickable(onClick = {
                        onProductClicked(item)
                    }),
                product = item,
                onFavoriteClick = {
                    onFavoriteClick(item.sku, item.isFavorite)
                }
            )
        }
    }
}

@Preview(name = "TopPicksItemsPreview", showBackground = true)
@Composable
fun TopPicksItemsPreview() {
    ProductsRow(
        data = getData(),
        horizontalPadding = 20,
        contentPadding = 10,
        onFavoriteClick = { _, _ -> },
    )
}

fun getData(): ImmutableList<Product> =
    persistentListOf(getMenuItem(), getMenuItem(), getMenuItem(), getMenuItem())

fun getMenuItem(): Product = object : Product {
    override val sku: String
        get() = 1.toString()
    override val name: String
        get() = "Chicken Ranch with 2 extra dips"
    override val shareLink: String
        get() = ""
    override val description: String
        get() = "product description"
    override val imageUrl: String
        get() = ""
    override val thumbnail: String?
        get() = null
    override val media: List<Product.Media>
        get() = emptyList()
    override val finalPrice: String
        get() = 90.toString()
    override val originalPrice: String
        get() = 100.toString()
    override val finalDisplayPrice: String
        get() = "0.0"
    override val originalDisplayPrice: String
        get() = "0.0"
    override val attributes: List<Product.Attribute>
        get() = emptyList()
    override val defaultAttributes: List<Product.Attribute>?
        get() = emptyList()
    override val tags: List<String>
        get() = listOf("TAG1", "TAG2")
    override val discountPercentage: String?
        get() = "10"
    override val isAvailable: Boolean
        get() = true
    override val notifyMeWhenAvailable: Boolean
        get() = false
    override val isFavorite: Boolean
        get() = false
}
