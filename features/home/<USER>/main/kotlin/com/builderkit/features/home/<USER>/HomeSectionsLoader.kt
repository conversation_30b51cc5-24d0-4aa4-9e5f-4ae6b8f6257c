package com.builderkit.features.home.data

import app.instakit.api.data.models.home.Section
import app.instakit.api.data.models.home.SectionContent
import com.builderkit.base.log.Reporter
import com.builderkit.base.utils.getOrDefault
import com.builderkit.features.home.extensions.LoyaltySection
import com.builderkit.features.home.mapper.toUiModel
import com.builderkit.features.home.model.Section as UiSection
import com.builderkit.repos.contracts.HomeRepoContract
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

/**
 * Responsible for loading home sections data following the Single Responsibility Principle.
 * This class handles only the loading of section data and transforming it to UI models.
 */
class HomeSectionsLoader(
    private val homeRepo: HomeRepoContract,
    private val reporter: Reporter
) {

    /**
     * Loads all home sections from the repository
     * @return List of Section objects or empty list if there's an error
     */
    suspend fun loadSections(): List<Section> {
        return homeRepo.fetchHomeSections()
            .map { it.getOrDefault { emptyList() } }
            .catch { error ->
                reporter.reportError(
                    message = "Error fetching home sections",
                    throwable = error
                )
                emit(emptyList())
            }.firstOrNull() ?: emptyList()
    }

    /**
     * Loads section content for all sections in parallel
     * @param sections List of sections to load content for
     * @param loyaltyPoints User loyalty points to include in loyalty section
     * @return List of UI section models
     */
    suspend fun loadSectionContents(
        sections: List<Section>,
        loyaltyPoints: String?
    ): List<UiSection> = coroutineScope {
        val sectionContentDeferred = sections.map { section ->
            async(Dispatchers.IO) {
                try {
                    val content: Flow<Result<SectionContent>> = when (section.type) {
                        Section.Type.Category -> homeRepo.fetchHomeCategoriesSection(section.id)
                        Section.Type.Product -> homeRepo.fetchHomeProductsSection(section.id)
                        Section.Type.Banner -> homeRepo.fetchHomeBannersSection(section.id)
                        Section.Type.Subcategory -> homeRepo.fetchHomeCategoriesSection(section.id)
                        Section.Type.CategoryItems -> homeRepo.fetchHomeProductsSection(section.id)
                        Section.Type.Loyalty -> flowOf(Result.success(LoyaltySection))
                        Section.Type.Unknown -> emptyFlow()
                    }
                    content.firstOrNull()?.getOrNull()
                        ?.toUiModel(
                            backgroundColor = section.backgroundColor,
                            backgroundImage = section.image,
                            sectionStyle = section.style,
                            loyaltyPoints = loyaltyPoints
                        )
                } catch (e: Exception) {
                    reporter.reportError(
                        message = "Error fetching section ${section.id}",
                        throwable = e
                    )
                    null
                }
            }
        }

        sectionContentDeferred.awaitAll().filterNotNull()
    }
}