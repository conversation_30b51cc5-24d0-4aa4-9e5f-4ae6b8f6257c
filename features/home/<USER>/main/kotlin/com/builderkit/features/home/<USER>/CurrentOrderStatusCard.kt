package com.builderkit.features.home.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.extensions.AsyncImage
import com.builderkit.base.ui.extensions.toPriceString
import com.builderkit.base.ui.model.Order
import com.builderkit.base.ui.model.Product
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.spacing
import com.builderkit.features.home.R
import com.builderkit.features.home.getData
import com.builderkit.base.R as BaseR

const val MAX_ITEMS_TO_SHOW = 2

@Composable
fun ActiveOrderCard(
    order: Order,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent),
        onClick = onClick,
        border = BorderStroke(
            width = 1.dp,
            color = Color(0xFFF0ECF8),
        ),
        elevation = CardDefaults.cardElevation(0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.small),
        ) {
            // Header section with Order ID and Date
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth(),
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(
                            id = R.string.order_id_placeholder,
                            order.id.toString(),
                        ),
                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Normal),
                        color = LocalColors.current.text,
                    )
                    Spacer(Modifier.height(MaterialTheme.spacing.xSmall))
                    Text(
                        text = order.createAt ?: "",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF6E726E), // Gray text color from design
                    )
                }

                Spacer(Modifier.width(MaterialTheme.spacing.small))

                if (order.shouldShowReorderButton) {
                    // Reorder button
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .clip(RoundedCornerShape(60.dp))
                            .background(Color(0xFFF0ECF8)) // Light purple background
                            .padding(horizontal = 10.dp, vertical = 6.dp)
                    ) {
                        Text(
                            text = stringResource(id = R.string.reorder),
                            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                            color = Color(0xFF673EB6), // Purple text color
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            painter = painterResource(id = BaseR.drawable.ic_reorder_arrow),
                            contentDescription = "Reorder",
                            tint = Color(0xFF673EB6), // Purple icon color
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            // Divider
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                thickness = 1.dp,
                color = Color(0xFFF0ECF8).copy(alpha = 0.5f) // Light purple with opacity
            )

            // Order items section
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // Image placeholders (would be replaced with actual product images)
                Box(
                    modifier = Modifier
                        .size(65.dp, 42.dp)
                ) {
                    order.items.forEachIndexed { index, product ->
                        val alignment =
                            if (order.items.size == 1) Alignment.Center else when (index) {
                                0 -> Alignment.CenterStart
                                1 -> Alignment.Center
                                else -> Alignment.CenterEnd
                            }

                        if (index > MAX_ITEMS_TO_SHOW) return@forEachIndexed

                        if (index < MAX_ITEMS_TO_SHOW) {
                            AsyncImage(
                                modifier = Modifier
                                    .size(41.dp)
                                    .clip(RoundedCornerShape(150.dp))
                                    .background(Color.LightGray)
                                    .align(alignment)
                                    .border(
                                        width = 1.dp,
                                        color = Color.White,
                                        shape = RoundedCornerShape(150.dp)
                                    ),
                                url = product.imageUrl.orEmpty(),
                                contentScale = ContentScale.Fit
                            )
                        } else {
                            Box(
                                modifier = Modifier
                                    .size(41.dp)
                                    .clip(RoundedCornerShape(150.dp))
                                    .background(Color(0xFFF8F8F8))
                                    .align(Alignment.CenterEnd)
                                    .border(
                                        width = 1.dp,
                                        color = Color.White,
                                        shape = RoundedCornerShape(150.dp)
                                    )
                            ) {
                                Text(
                                    text = "+${order.items.size - MAX_ITEMS_TO_SHOW}",
                                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                                    color = LocalColors.current.text,
                                    modifier = Modifier.align(Alignment.Center)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.width(12.dp))

                // Order details
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Super Tasty Cheese Pizza, Burger Sandwich",
                        style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                        color = LocalColors.current.text,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.xSmall))
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = stringResource(id = R.string.total),
                            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold),
                            color = LocalColors.current.text,
                        )
                        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xxSmall))
                        Text(
                            text = "${order.total.toPriceString()} ${stringResource(R.string.currency)}",
                            style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                            color = LocalColors.current.text,
                        )

                        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xSmall))

                        Text(
                            text = pluralStringResource(
                                id = R.plurals.items_count_placeholder,
                                order.items. 
                            ),
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF6E726E), // Gray text color
                        )
                    }
                }
            }

            // Rating section
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
                    .clip(RoundedCornerShape(6.dp))
                    .background(Color(0xFFFFF6E5)) // Light orange background
                    .padding(horizontal = 8.dp, vertical = 4.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.rate_now),
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                    color = Color(0xFFFEB12B), // Orange text color
                )

                Spacer(modifier = Modifier.weight(1f))

                // Star rating icons
                repeat(5) {
                    Icon(
                        painter = painterResource(id = BaseR.drawable.ic_star_rating),
                        contentDescription = "Star",
                        tint = Color(0xFFFEB12B), // Orange star color
                        modifier = Modifier
                            .padding(horizontal = 2.dp)
                            .size(20.dp)
                    )
                }
            }
        }
    }
}


@Preview(name = "ActiveOrderCardPreview")
@Composable
fun ActiveOrderCardPreview() {
    BuilderKitTheme {
        Surface {
            ActiveOrderCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                order = getDummyOrder(),
            )
        }
    }
}

fun getDummyOrder() = object : Order {
    override val id: Long
        get() = 1
    override val status: app.instakit.api.data.models.order.Order.OrderStatus
        get() = app.instakit.api.data.models.order.Order.OrderStatus.PENDING
    override val arrivingTime: Long
        get() = 1694907046215
    override val total: Double
        get() = 200.0
    override val currency: String
        get() = "EGP"
    override val items: List<Product>
        get() = getData()
    override val createAt: String = "Aug 17"
}
