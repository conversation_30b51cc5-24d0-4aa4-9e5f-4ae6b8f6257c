package com.builderkit.features.home.data

import com.builderkit.features.home.HomeUIData
import com.builderkit.features.home.mapper.toUiModel
import com.builderkit.features.home.model.Section
import com.builderkit.repos.contracts.HomeRepoContract
import com.builderkit.repos.contracts.OrdersRepoContract
import com.builderkit.repos.contracts.PickupBranchRepoContract
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

/**
 * Responsible for combining different data sources to create the HomeUIData state.
 * This class follows the Single Responsibility Principle by handling only data combination logic.
 */
class HomeDataCombiner(
    private val homeRepo: HomeRepoContract,
    private val pickupBranchRepo: PickupBranchRepoContract,
    private val ordersRepo: OrdersRepoContract,
    private val locationProvider: LocationDataProvider
) {

    /**
     * Combines all necessary data to create the HomeUIData state
     * @param sections The UI sections to display
     * @param isUserAuthenticated Whether the user is authenticated
     * @param unreadNotificationsCount The count of unread notifications
     * @return Flow of HomeUIData
     */
    fun combineHomeData(
        sections: List<Section>,
        isUserAuthenticated: Boolean,
        unreadNotificationsCount: StateFlow<Int>
    ): Flow<HomeUIData> {
        val ordersFlow = if (isUserAuthenticated) {
            ordersRepo.fetchCurrentRunningOrder()
        } else {
            flowOf(Result.success(emptyList()))
        }
        return ordersFlow.map { orders ->
            HomeUIData(
                sections = sections,
                services = emptyList(),
                selectedBranchName = null,
                selectedDeliveryAddress = null,
                orders = if (isUserAuthenticated) orders.getOrNull()?.toUiModel() else null,
                unreadNotificationsCount = unreadNotificationsCount
            )
        }
    }
}