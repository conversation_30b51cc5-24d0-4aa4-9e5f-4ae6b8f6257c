package com.builderkit.features.location.di

import androidx.activity.ComponentActivity
import com.builderkit.base.permissions.PermissionsManager
import com.builderkit.features.location.permission.LocationPermissionManager
import com.builderkit.features.location.pickup.PickupBranchViewModel
import com.builderkit.features.location.places.GooglePlacesSearchClient
import com.builderkit.features.location.places.PlacesSearch
import com.builderkit.features.location.viewmodel.LocationViewModel
import com.builderkit.features.location.viewmodel.PlacesSuggestionViewModel
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.androidx.viewmodel.dsl.viewModelOf
import org.koin.dsl.module

val locationModule = module {

    factory<PlacesSearch> {
        GooglePlacesSearchClient(context = androidContext())
    }

    viewModelOf(::PlacesSuggestionViewModel)

    viewModel { (permissionManager: PermissionsManager) ->
        LocationViewModel(
            repo = get(),
            userRepo = get(),
            pickupBranchRepoContract = get(),
            permissionsManager = permissionManager,
            tracker = get(),
            settingsRepo = get()
        )
    }

    viewModel { (permissionManager: PermissionsManager) ->
        PickupBranchViewModel(repo = get(), permissionsManager = permissionManager)
    }

    factory<PermissionsManager> { (activity: ComponentActivity) ->
        LocationPermissionManager(activity)
    }
}