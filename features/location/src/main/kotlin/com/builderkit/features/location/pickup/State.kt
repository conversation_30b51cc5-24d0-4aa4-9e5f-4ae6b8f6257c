package com.builderkit.features.location.pickup

import app.instakit.api.data.models.branches.Branch
import app.instakit.api.utils.ApiResult
import com.builderkit.base.ui.errors.UIError

sealed class State {

    data object Initial : State()

    data object Loading : State()

    data object ShowingLocationPermissionDialog : State()

    data object LocationPermissionGranted : State()

    data class LatLngObtained(val lat: Double, val lng: Double) : State()

    data class Success(val branches: List<Branch>, val selectedBranch: Branch?) : State()

    data class Error(val error: UIError) : State()

    companion object {
        fun ApiResult<List<Branch>>.mapToState(selectedBranchName: String?): State = when {
            this.error != null -> Error(UIError.GenericError)
            else -> Success(
                branches = this.result?.filter { it.active } ?: emptyList(),
                selectedBranch = this.result?.find { it.name == selectedBranchName },
            )
        }
    }
}
