package com.builderkit.features.authentication.screen

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.LineBreak
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.builderkit.base.ui.components.AppButton
import com.builderkit.base.ui.components.AppText
import com.builderkit.base.ui.components.OtpField
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.spacing
import com.builderkit.features.authentication.R
import com.builderkit.base.R as baseR
import com.builderkit.features.authentication.VerifyUIState
import com.builderkit.features.authentication.components.AuthSurface
import com.builderkit.features.authentication.verifyotp.VerifyOtpCommand
import com.builderkit.features.authentication.verifyotp.commands.BackToIdleCommand
import com.builderkit.features.authentication.verifyotp.commands.FetchUserAddressesCommand
import com.builderkit.features.authentication.verifyotp.commands.ResendOtpCommand

@Composable
fun VerificationScreen(
    state: VerifyUIState,
    fullPhoneNumber: String,
    timerCountdown: String,
    couldResendCode: Boolean,
    modifier: Modifier = Modifier,
    onBackPressed: () -> Unit = {},
    onCommand: (VerifyOtpCommand) -> Unit = {},
    onVerifySuccess: () -> Unit = {},
) {
    var otpValue by remember { mutableStateOf("") }
    var isValidOTP by remember { mutableStateOf(false) }

    LaunchedEffect(key1 = state) {
        when (state) {
            VerifyUIState.VerifySuccess -> {
                onCommand(FetchUserAddressesCommand)
            }

            VerifyUIState.AddressesLoadedSuccess, VerifyUIState.AddressesLoadedError -> {
                onVerifySuccess()
            }

            else -> {}
        }
    }

    LaunchedEffect(isValidOTP) {
        if (isValidOTP) {
            onCommand(
                com.builderkit.features.authentication.verifyotp.commands.VerifyOtpCommand(
                    otpValue,
                ),
            )
        }
    }

    AuthSurface(
        modifier = modifier.fillMaxSize(),
        title = stringResource(id = R.string.verify_your_account),
        description = stringResource(id = R.string.verify_account_message),
        onBackClick = onBackPressed,
        content = {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = MaterialTheme.spacing.large),
            ) {
                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontWeight = FontWeight(400),
                                fontSize = 14.sp,
                                lineBreak = LineBreak.Simple,
                                color = LocalColors.current.body,
                            ).toSpanStyle(),
                        ) {
                            append(stringResource(id = R.string.we_have_sent_you_the_verification_code_to))
                        }
                        withStyle(
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontWeight = FontWeight(400),
                                fontSize = 14.sp,
                                lineBreak = LineBreak.Simple,
                                color = LocalColors.current.primary,
                            ).toSpanStyle(),
                        ) {
                            append(" $fullPhoneNumber ")
                        }
                        withStyle(
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontWeight = FontWeight(400),
                                fontSize = 14.sp,
                                lineBreak = LineBreak.Simple,
                                color = LocalColors.current.body,
                            ).toSpanStyle(),
                        ) {
                            append(stringResource(id = R.string.enter_the_code))
                        }
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                OtpField(invalidInput = state == VerifyUIState.InputError) { otp, validOTP ->
                    onCommand(BackToIdleCommand)
                    isValidOTP = validOTP
                    otpValue = otp
                }
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                ) {
                    if (state == VerifyUIState.ResendOtpLoading) {
                        CircularProgressIndicator(
                            color = LocalColors.current.primary,
                        )
                    } else {
                        Text(
                            text = stringResource(id = R.string.did_not_receive_code),
                            style = MaterialTheme.typography.bodySmall.copy(
                                textAlign = TextAlign.Center,
                                fontWeight = FontWeight(400),
                                fontSize = 16.sp,
                                color = LocalColors.current.text,
                            ),
                        )
                        Spacer(modifier = Modifier.width(5.dp))
                        if (couldResendCode) {
                            AppText(
                                text = stringResource(id = R.string.send_again),
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    fontWeight = FontWeight(600),
                                    fontSize = 16.sp,
                                    color = LocalColors.current.accent,
                                ),
                                modifier = Modifier.clickable {
                                    onCommand(ResendOtpCommand)
                                },
                            )
                        } else {
                            Text(
                                text = stringResource(id = R.string.resend_code_in),
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight(600),
                                    fontSize = 16.sp,
                                    color = LocalColors.current.accent,
                                ),
                            )
                            Spacer(modifier = Modifier.width(5.dp))
                            Text(
                                text = timerCountdown,
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight(600),
                                    fontSize = 16.sp,
                                    color = LocalColors.current.accent,
                                ),
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
            }
        },
        stickyFooter = {
            Column(
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xxLarge),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                AppButton(
                    contentText = stringResource(id = baseR.string.confirm),
                    enabled = isValidOTP && state != VerifyUIState.ResendOtpLoading,
                    loading = state == VerifyUIState.Loading || state == VerifyUIState.AddressesLoading,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    onCommand(
                        com.builderkit.features.authentication.verifyotp.commands.VerifyOtpCommand(
                            otpValue,
                        ),
                    )
                }
                Spacer(modifier = Modifier.height(15.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                ) {
                    Text(
                        text = stringResource(id = R.string.wrong_phone_number),
                        style = MaterialTheme.typography.bodySmall.copy(
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight(400),
                            fontSize = 14.sp,
                            color = LocalColors.current.body,
                        ),
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                    Text(
                        text = stringResource(id = R.string.go_back),
                        style = MaterialTheme.typography.bodySmall.copy(
                            textAlign = TextAlign.Center,
                            fontWeight = FontWeight(600),
                            fontSize = 16.sp,
                            color = LocalColors.current.primary,
                        ),
                        modifier = Modifier.clickable { onBackPressed() },
                    )
                }
                Spacer(modifier = Modifier.height(15.dp))
            }
        }
    )
}

@Preview
@Composable
fun VerificationScreenPreview() {
    BuilderKitTheme {
        VerificationScreen(
            state = VerifyUIState.VerifyIdle,
            fullPhoneNumber = "+20 1234 56789",
            timerCountdown = "00:34",
            couldResendCode = false,
        )
    }
}

@Preview
@Composable
fun VerificationScreenPreviewResendLoading() {
    BuilderKitTheme {
        VerificationScreen(
            state = VerifyUIState.ResendOtpLoading,
            fullPhoneNumber = "+20 1234 56789",
            timerCountdown = "00:34",
            couldResendCode = true,
        )
    }
}

@Preview
@Composable
fun VerificationScreenResendOTPPreview() {
    BuilderKitTheme {
        VerificationScreen(
            state = VerifyUIState.VerifyIdle,
            fullPhoneNumber = "+20 1234 56789",
            timerCountdown = "00:34",
            couldResendCode = true,
        )
    }
}
