package com.builderkit.features.profile.screens.account.components

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import app.instakit.api.data.models.user.Gender
import com.builderkit.features.profile.R

@Composable
internal fun PersonalInfoComponent(
    dateOfBirth: String?,
    gender: Gender?,
    onGenderClick: () -> Unit,
    onBirthdateClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AccountGroupComponent(
        modifier = modifier,
        title = stringResource(R.string.personal_info),
        content = {
            AccountItemComponent(
                title = stringResource(R.string.date_of_birth),
                titleIcon = R.drawable.ic_calendar,
                details = dateOfBirth.orEmpty(),
                onClick = onBirthdateClick
            )
            AccountItemComponent(
                title = stringResource(R.string.gender),
                titleIcon = R.drawable.ic_gender,
                details = gender?.let { getGenderString(it) } ?: "",
                onClick = onGenderClick
            )
        }
    )
}


@Composable
private fun getGenderString(gender: Gender): String {
    val genderStringRes = when (gender) {
        Gender.MALE -> R.string.male
        Gender.FEMALE -> R.string.female
    }
    return stringResource(genderStringRes)
}

@Preview
@Composable
private fun PersonalInfoComponentPreview() {
    PersonalInfoComponent(
        dateOfBirth = "12/12/2000",
        gender = Gender.MALE,
        onGenderClick = {},
        onBirthdateClick = {}
    )
}