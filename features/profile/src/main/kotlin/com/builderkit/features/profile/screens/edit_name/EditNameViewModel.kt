package com.builderkit.features.profile.screens.edit_name

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.instakit.api.data.models.user.User
import com.builderkit.features.profile.screens.edit_name.commands.EditNameCommandsReceiver
import com.builderkit.repos.contracts.ProfileRepoContract
import com.builderkit.repos.contracts.UserRepoContract
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class EditNameViewModel(
    private val profileRepo: ProfileRepoContract,
    private val userRepo: UserRepoContract
) : ViewModel(), EditNameCommandsReceiver {

    private val _uiState = MutableStateFlow<EditNameState>(EditNameState.Loading)
    val uiState: StateFlow<EditNameState> = _uiState

    private val _effect: Channel<EditNameSideEffect> = Channel()
    val effect = _effect.receiveAsFlow()

    private val _user = MutableStateFlow<User?>(null)
    val user: StateFlow<User?> = _user

    init {
        fetchUser()
    }

    private fun fetchUser() {
        viewModelScope.launch {
            userRepo.fetchUser().collect {
                if (user.value == null) {
                    _uiState.update { EditNameState.Idle }
                }
                _user.value = it
            }
        }
    }

    override fun updateName(firstName: String, lastName: String) {
        _uiState.update { EditNameState.Loading }
        viewModelScope.launch {
            profileRepo.updateUserName(
                firstName = firstName,
                lastName = lastName
            ).collect {
                it.exceptionOrNull()?.let { error ->
                    _effect.trySend(EditNameSideEffect.ShowMessage(error.message))
                    _uiState.update { EditNameState.Idle }
                } ?: run {
                    _uiState.update { EditNameState.ProfileUpdated }
                }
            }
        }
    }

}