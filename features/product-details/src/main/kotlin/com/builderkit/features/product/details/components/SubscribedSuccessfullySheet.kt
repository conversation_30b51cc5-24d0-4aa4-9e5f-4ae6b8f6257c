package com.builderkit.features.product.details.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.builderkit.base.ui.components.AppButton
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.features.product.details.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SubscribedSuccessfullyBottomSheet(
    sheetState: SheetState,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ModalBottomSheet(
        onDismissRequest = {},
        dragHandle = {},
        modifier = modifier,
        shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp),
        sheetState = sheetState,
    ) {
        SubscribedSuccessfullyComponent(
            modifier = Modifier.padding(20.dp),
            onDismiss = onDismiss,
        )
    }
}

@Composable
private fun SubscribedSuccessfullyComponent(
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit,
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = R.drawable.ic_subscribed),
            contentDescription = null,
        )
        Spacer(modifier = Modifier.height(9.dp))
        Text(
            text = stringResource(id = R.string.subscribed_successfully),
            style =
                MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.W600,
                    lineHeight = 22.sp,
                    color = LocalColors.current.text,
                ),
        )
        Text(
            text = stringResource(id = R.string.notify_me_message),
            textAlign = TextAlign.Center,
            style =
                MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.W400,
                    lineHeight = 22.sp,
                    color = LocalColors.current.body,
                ),
        )
        Spacer(modifier = Modifier.height(20.dp))
        AppButton(
            contentText = stringResource(id = R.string.ok),
            modifier = Modifier.fillMaxWidth(),
            onClick = onDismiss,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun SubscribedSuccessfullyComponentPreview() {
    BuilderKitTheme {
        SubscribedSuccessfullyComponent(
            modifier = Modifier.fillMaxWidth().padding(20.dp),
            onDismiss = {},
        )
    }
}
