package com.builderkit.features.product.details.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.builderkit.base.ui.extensions.AsyncImage
import com.builderkit.base.ui.model.DataList
import com.builderkit.base.ui.model.Product
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.White
import com.builderkit.features.product.details.R
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ProductMediaComponent(
    media: DataList<Product.Media>,
    selectedIndex: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    Box(modifier = modifier) {
        val state = rememberPagerState {
            media.items.size
        }
        HorizontalPager(state = state) {
            MediaComponent(
                item = media.items[it],
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(onClick = onClick)
            )
        }
        LaunchedEffect(key1 = selectedIndex) {
            coroutineScope.launch {
                state.animateScrollToPage(selectedIndex)
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
internal fun ProductMediaComponentPreview() {
    BuilderKitTheme {
        ProductMediaComponent(
            media = DataList(getMediaItems()),
            selectedIndex = 0,
            onClick = {},
        )
    }
}

internal fun getMediaItems(): List<Product.Media> = listOf(
    object : Product.Media {
        override val type: Product.Media.Type
            get() = Product.Media.Type.IMAGE
        override val url: String
            get() = ""
    },
    object : Product.Media {
        override val type: Product.Media.Type
            get() = Product.Media.Type.IMAGE
        override val url: String
            get() = ""
    },
)

@Composable
internal fun MediaComponent(item: Product.Media, modifier: Modifier = Modifier) {
    val requiredModifier = modifier
        .aspectRatio(374 / 268f)
    when (item.type) {
        Product.Media.Type.IMAGE -> ImageComponent(
            url = item.url,
            modifier = requiredModifier,
        )

        Product.Media.Type.VIDEO -> VideoComponent(
            url = item.url,
            modifier = requiredModifier.background(color = Color.Black),
        )
    }
}

@Preview(showBackground = true)
@Composable
internal fun MediaComponentPreview() {
    BuilderKitTheme {
        MediaComponent(item = getMediaItems().first(), modifier = Modifier)
    }
}

@Composable
internal fun ImageComponent(url: String, modifier: Modifier = Modifier) {
    AsyncImage(url = url, contentScale = ContentScale.FillBounds, modifier = modifier)
}

@Preview(showBackground = true)
@Composable
internal fun ImageComponentPreview() {
    BuilderKitTheme {
        ImageComponent("")
    }
}

@Composable
internal fun VideoComponent(url: String, modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val player = remember {
        ExoPlayer.Builder(context).build().apply {
            addMediaItem(MediaItem.fromUri(url))
            playWhenReady = true
            prepare()
        }
    }
    AndroidView(factory = {
        PlayerView(context).apply {
            this.player = player
            useController = false
            player.play()
        }
    }, modifier = modifier)
}

@Preview
@Composable
internal fun VideoComponentPreview() {
    BuilderKitTheme {
        VideoComponent("")
    }
}

@Composable
fun MediaThumbnailComponent(
    item: Product.Media,
    modifier: Modifier = Modifier,
    onThumbnailClicked: (url: String) -> Unit = {},
) {
    ThumbnailComponent(
        url = item.url,
        modifier = modifier,
        onClick = { onThumbnailClicked(item.url) },
    )
}

@Preview
@Composable
fun MediaThumbnailComponentPreview() {
    BuilderKitTheme {
        MediaThumbnailComponent(
            item = object : Product.Media {
                override val type: Product.Media.Type
                    get() = Product.Media.Type.IMAGE
                override val url: String
                    get() = ""
            },
            onThumbnailClicked = {},
        )
    }
}

@Composable
fun ThumbnailComponent(url: String, modifier: Modifier = Modifier, onClick: () -> Unit = {}) {
    Surface(
        color = White,
        modifier = modifier
            .background(color = White, shape = RoundedCornerShape(10.dp))
            .clickable(onClick = onClick)
            .padding(12.dp),
    ) {
        AsyncImage(
            url = url,
            modifier = Modifier
                .requiredSize(24.dp),
            placeholder = R.drawable.media_placeholder,
            contentScale = ContentScale.Fit,
        )
    }
}

@Preview
@Composable
fun ThumbnailComponentPreview() {
    BuilderKitTheme {
        ThumbnailComponent("")
    }
}
