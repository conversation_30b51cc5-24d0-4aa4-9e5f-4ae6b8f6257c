package com.builderkit.features.product.details.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.requiredSizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.builderkit.base.ui.extensions.AsyncImage
import com.builderkit.base.ui.extensions.toDoubleOrZero
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.features.product.details.R
import com.builderkit.features.product.details.models.ProductAttributeOption

@Composable
fun AttributeOption(
    option: ProductAttributeOption,
    modifier: Modifier = Modifier,
    button: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier = modifier,
        verticalAlignment = androidx.compose.ui.Alignment.CenterVertically,
    ) {
        AsyncImage(
            url = option.image ?: "",
            modifier = Modifier.requiredSizeIn(maxWidth = 44.dp, maxHeight = 44.dp),
            contentScale = ContentScale.Fit,
            placeholder = R.drawable.ic_option,
            errorPlaceholder = R.drawable.ic_option,
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = option.name,
                style = MaterialTheme.typography.bodyMedium.copy(
                    lineHeight = 19.sp,
                    color = LocalColors.current.text,
                    fontWeight = FontWeight.SemiBold,
                ),
            )
            if (option.price.toDoubleOrZero() > 0) {
                Text(
                    text = "${option.price} ${stringResource(com.builderkit.base.R.string.currency)}",
                    style = MaterialTheme.typography.bodySmall.copy(
                        lineHeight = 19.sp,
                        color = LocalColors.current.text,
                        fontWeight = FontWeight.Normal,
                    ),
                )
            }
        }
        button()
    }
}

@Preview(showBackground = true)
@Composable
fun OptionComponentPreview() {
    BuilderKitTheme {
        AttributeOption(
            option = ProductAttributeOption(
                id = 1,
                name = "Option 1",
                price = "10.0",
                index = 0,
                value = "",
                image = null,
                isSelected = true,
            ),
            button = {
                RadioButton(selected = true, onClick = {})
            },
        )
    }
}
