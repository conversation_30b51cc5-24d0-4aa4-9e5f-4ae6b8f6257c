package com.builderkit.navigation.search

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.builderkit.base.ui.commands.processCommand
import com.builderkit.features.search.SearchScreen
import com.builderkit.features.search.SearchViewModel
import com.builderkit.navigation.AppRoutes
import org.koin.androidx.compose.koinViewModel

internal fun NavGraphBuilder.search(
    toCategory: (categoryId: Long, name: String) -> Unit,
    toProductDetails: (productSku: String) -> Unit
) {
    composable(AppRoutes.Search.path) {
        val searchViewModel: SearchViewModel = koinViewModel()
        val state by searchViewModel.state.collectAsStateWithLifecycle()
        val searchQuery by searchViewModel.searchQuery.collectAsStateWithLifecycle()

        SearchScreen(
            state = state,
            query = searchQuery,
            onSearchQueryChanged = searchViewModel::onSearchQueryChanged,
            toCategory = toCategory,
            toProductDetails = toProductDetails,
            onCommand = searchViewModel::processCommand,
            modifier = Modifier.fillMaxSize()
        )
    }
}