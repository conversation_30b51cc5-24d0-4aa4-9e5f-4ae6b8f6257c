package com.builderkit.navigation.profile

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import app.instakit.api.data.models.settings.FAQ
import com.builderkit.base.ui.components.AppBarScreen
import com.builderkit.base.ui.model.DataList
import com.builderkit.core.navigation.R
import com.builderkit.features.faq.FaqScreen
import com.builderkit.navigation.AppRoutes

fun NavGraphBuilder.faq(faqs: List<FAQ>, onBackClicked: () -> Unit = {}) {
    composable(AppRoutes.Faq.path) {
        AppBarScreen(
            appBarTitle = stringResource(id = R.string.faqs),
            leadingIcon = com.builderkit.base.R.drawable.ic_back,
            leadingIconClicked = onBackClicked
        ) {
            FaqScreen(faqs = DataList(faqs), modifier = Modifier.fillMaxSize())
        }
    }
}

fun NavController.navigateToFaqs() {
    navigate(AppRoutes.Faq.path)
}