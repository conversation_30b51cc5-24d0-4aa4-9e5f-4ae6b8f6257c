package com.builderkit.navigation.addressbook

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.builderkit.base.ui.commands.processCommand
import com.builderkit.base.ui.components.AppBarScreen
import com.builderkit.core.navigation.R
import com.builderkit.features.addressbook.AddressBookListingScreen
import com.builderkit.features.addressbook.AddressBookListingViewModel
import com.builderkit.navigation.AppRoutes
import org.koin.androidx.compose.koinViewModel

internal fun NavGraphBuilder.addressBook(
    onBackClicked: () -> Unit = {},
    onAddNewAddressClicked: () -> Unit = {},
    onEditAddress: (addressId: Long) -> Unit = {},
) {
    composable(AppRoutes.AddressBook.path) {
        val viewModel: AddressBookListingViewModel = koinViewModel()
        val state by viewModel.uiState.collectAsStateWithLifecycle()
        AppBarScreen(
            appBarTitle = stringResource(id = R.string.saved_addresses),
            leadingIcon = com.builderkit.base.R.drawable.ic_back,
            leadingIconClicked = onBackClicked,
            optionItems = {
                Icon(
                    painter = painterResource(id = com.builderkit.features.addressbook.R.drawable.ic_add_circle),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 24.dp)
                        .clickable(onClick = onAddNewAddressClicked),
                )
            },
        ) {
            AddressBookListingScreen(
                state = state,
                onEditAddress = onEditAddress,
                onCommand = viewModel::processCommand,
                onAddNewAddressClicked = onAddNewAddressClicked,
            )
        }
    }
}

internal fun NavController.navigateAddressBook() {
    navigate(AppRoutes.AddressBook.path)
}
