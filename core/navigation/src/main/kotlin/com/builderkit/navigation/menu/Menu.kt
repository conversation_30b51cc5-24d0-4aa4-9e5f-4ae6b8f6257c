package com.builderkit.navigation.menu

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.builderkit.base.ui.commands.processCommand
import com.builderkit.base.ui.components.AppBarScreen
import com.builderkit.base.ui.components.AppBarWithTabs
import com.builderkit.base.ui.components.tabs.AppDefaultTabsRow
import com.builderkit.core.navigation.R
import com.builderkit.features.product.Args
import com.builderkit.features.product.menu.MenuScreen
import com.builderkit.features.product.menu.MenuTheme
import com.builderkit.features.product.menu.TabRowStyle
import com.builderkit.features.product.states.MenuLoading
import com.builderkit.features.product.states.MenuUIData
import com.builderkit.features.product.viewmodels.MenuViewModel
import com.builderkit.navigation.AppRoutes
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import org.koin.androidx.compose.koinViewModel
import org.koin.core.parameter.parametersOf

val theme = MenuTheme.ONE_PAGE
val menuConfig = theme.createConfig(
    tabLevels = 2,
    tabRowStyle = TabRowStyle.Default,
)

@OptIn(ExperimentalMaterial3Api::class)
internal fun NavGraphBuilder.menu(
    navigateToWishlist: () -> Unit,
    onProductClicked: (productSku: String) -> Unit = {},
    navigateToSubCategories: (parentCategoryId: Long) -> Unit = {},
    navigateToProductsListing: (categoryId: String, categoryName: String) -> Unit = { _, _ -> },
) {
    composable(AppRoutes.Menu.path) {
        val menuViewModel: MenuViewModel =
            koinViewModel(parameters = { parametersOf(theme, null, menuConfig.tabLevels ?: 0) })
        val state by menuViewModel.uiState.collectAsStateWithLifecycle()
        val categoriesTabs = (state as? MenuUIData)?.categories?.toImmutableList()
        var selectedIndex by remember { mutableIntStateOf(0) }
        val onTapSelected: (index: Int) -> Unit = { index ->
            selectedIndex = index
            menuViewModel.updateStateWithSelectedIndex(index)
        }
        when {
            theme == MenuTheme.ONE_PAGE && menuConfig.tabRowStyle == TabRowStyle.Default -> AppBarWithTabs(
                screenTitle = stringResource(id = R.string.menu),
                isScreenLoading = state is MenuLoading,
                tabs = {
                    AppDefaultTabsRow(
                        isLoading = state is MenuLoading,
                        tabs = categoriesTabs?.map { it.name }?.toImmutableList()
                            ?: persistentListOf(),
                        selectedIndex = selectedIndex,
                        onTabSelected = onTapSelected,
                    )
                }
            ) {
                MenuScreen(
                    state = state,
                    modifier = Modifier.fillMaxSize(),
                    onProductClicked = onProductClicked,
                    sideEffect = menuViewModel.sideEffect,
                    onCommand = menuViewModel::processCommand,
                    dataPagination = menuViewModel.paginatedData,
                    theme = theme,
                )
            }

            theme == MenuTheme.ONE_PAGE && menuConfig.tabRowStyle is TabRowStyle.Custom -> {
                Column(modifier = Modifier.fillMaxSize()) {
                    TopAppBar(
                        title = {
                            Text(
                                text = stringResource(id = R.string.menu),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth(),
                            )
                        },
                        actions = {
                            IconButton(onClick = navigateToWishlist) {
                                Icon(
                                    painter = painterResource(id = com.builderkit.base.R.drawable.ic_heart),
                                    contentDescription = "",
                                )
                            }
                        },
                    )
                    menuConfig.tabRowStyle.content(
                        state is MenuLoading,
                        categoriesTabs ?: persistentListOf(),
                        selectedIndex,
                        onTapSelected,
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    MenuScreen(
                        state = state,
                        modifier = Modifier.weight(1f),
                        onProductClicked = onProductClicked,
                        sideEffect = menuViewModel.sideEffect,
                        onCommand = menuViewModel::processCommand,
                        dataPagination = menuViewModel.paginatedData,
                        theme = theme,
                        changeCategorySelection = { categoryId ->
                            val index = categoriesTabs?.indexOfFirst { it.id == categoryId }
                            if (index != null && index != -1) {
                                onTapSelected(index)
                            }
                        }
                    )
                }
            }

            theme == MenuTheme.MULTIPLE_PAGES -> AppBarScreen(appBarTitle = stringResource(id = R.string.menu)) {
                MenuScreen(
                    state = state,
                    modifier = Modifier.fillMaxSize(),
                    onProductClicked = onProductClicked,
                    sideEffect = menuViewModel.sideEffect,
                    onCommand = menuViewModel::processCommand,
                    dataPagination = menuViewModel.paginatedData,
                    theme = theme,
                    onCategoryClicked = { category ->
                        if (category.subCategories.isNotEmpty()) {
                            navigateToSubCategories(category.id)
                            return@MenuScreen
                        }
                        navigateToProductsListing(
                            category.id.toString(),
                            category.name,
                        )
                    }
                )
            }
        }
    }
}

internal fun NavGraphBuilder.subCategories(
    navigateToSubCategories: (parentCategoryId: Long) -> Unit,
    navigateToProductsListing: (categoryId: String, categoryName: String) -> Unit,
    onBackClicked: () -> Unit = {},
) {
    composable(AppRoutes.Subcategory.path) {
        val menuViewModel: MenuViewModel =
            koinViewModel(
                parameters = {
                    parametersOf(
                        theme,
                        it.arguments?.getString(Args.CATEGORY_ID)?.toLong(),
                        menuConfig.tabLevels ?: 0
                    )
                },
            )
        val state by menuViewModel.uiState.collectAsStateWithLifecycle()

        AppBarScreen(
            appBarTitle = stringResource(id = R.string.menu),
            leadingIcon = com.builderkit.base.R.drawable.ic_back,
            leadingIconClicked = onBackClicked,
        ) {
            MenuScreen(
                state = state,
                modifier = Modifier.fillMaxSize(),
                sideEffect = menuViewModel.sideEffect,
                onCommand = menuViewModel::processCommand,
                dataPagination = menuViewModel.paginatedData,
                theme = theme,
                onCategoryClicked = { category ->
                    if (category.subCategories.isNotEmpty()) {
                        navigateToSubCategories(category.id)
                        return@MenuScreen
                    }
                    navigateToProductsListing(
                        category.id.toString(),
                        category.name,
                    )
                }
            )
        }
    }
}

internal fun NavController.navigateToSubcategory(categoryId: Long) {
    navigate(AppRoutes.Subcategory.path.replace("{${Args.CATEGORY_ID}}", categoryId.toString()))
}
