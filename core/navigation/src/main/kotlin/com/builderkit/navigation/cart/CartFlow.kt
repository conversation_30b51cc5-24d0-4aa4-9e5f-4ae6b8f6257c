package com.builderkit.navigation.cart

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.builderkit.base.ui.NavigationItem
import com.builderkit.navigation.AppRoutes
import com.builderkit.navigation.checkout.checkout
import com.builderkit.navigation.checkout.navigateToCheckout

fun NavGraphBuilder.cartFlow(
    onDestinationChange: (shouldShowBottomBar: Boolean, fullScreen: Boolean) -> Unit,
    onStartShoppingClicked: () -> Unit = {},
    onNavigationToAuth: () -> Unit = {},
    onNavigateToChooseDeliveryAddress: () -> Unit = {},
    onNavigateToChoosePickupBranch: () -> Unit = {},
    onPlacingOrderSuccess: (orderId: String, isPlacingOrderSuccess: Boolean) -> Unit = { _, _ -> },
    onNavigationToEditAddress: (addressId: Long) -> Unit = {},
    onNavigateToCreateAddress: () -> Unit = {},
) {
    composable(NavigationItem.CART.name) {
        CartGraph(
            onStartShoppingClicked = onStartShoppingClicked,
            onDestinationChange = onDestinationChange,
            onNavigationToAuth = onNavigationToAuth,
            onNavigationToChooseDeliveryAddress = onNavigateToChooseDeliveryAddress,
            onNavigationToChoosePickupBranch = onNavigateToChoosePickupBranch,
            onPlacingOrderSuccess = onPlacingOrderSuccess,
            onNavigationToEditAddress = onNavigationToEditAddress,
            onNavigateToCreateAddress = onNavigateToCreateAddress,
        )
    }
}

@Composable
internal fun CartGraph(
    onDestinationChange: (shouldShowBottomBar: Boolean, fullScreen: Boolean) -> Unit,
    onStartShoppingClicked: () -> Unit = {},
    onNavigationToAuth: () -> Unit = {},
    onNavigationToChooseDeliveryAddress: () -> Unit = {},
    onNavigationToChoosePickupBranch: () -> Unit = {},
    onPlacingOrderSuccess: (orderId: String, isPlacingOrderSuccess: Boolean) -> Unit = { _, _ -> },
    onNavigationToEditAddress: (addressId: Long) -> Unit = {},
    onNavigateToCreateAddress: () -> Unit = {},
) {
    val navController = rememberNavController()
    val fullScreenRoutes by remember { mutableStateOf(AppRoutes.getFullScreenRoutes()) }
    navController.addOnDestinationChangedListener { _, destination, _ ->
        onDestinationChange(
            destination.route != AppRoutes.Checkout.path,
            destination.route in fullScreenRoutes
        )
    }
    NavHost(navController = navController, startDestination = AppRoutes.Cart.path) {
        cart(
            onStartShoppingClicked = onStartShoppingClicked,
            onCheckoutClicked = {
                navController.navigateToCheckout()
            },
            onNavigationToAuth = onNavigationToAuth,
            onNavigationToChooseDeliveryAddress = onNavigationToChooseDeliveryAddress,
            onNavigationToPickupBranchSelection = onNavigationToChoosePickupBranch,
            onNavigationToEditAddress = onNavigationToEditAddress,
            onNavigateToCreateAddress = onNavigateToCreateAddress,
        )
        checkout(
            onBackClicked = navController::popBackStack,
            onOrderPlacedSuccess = onPlacingOrderSuccess,
        )
    }
}
