package com.builderkit.navigation.profile

import com.builderkit.features.profile.screens.edit_email.EditEmailScreen
import com.builderkit.features.profile.screens.edit_email.EditEmailViewModel
import com.builderkit.navigation.AppRoutes
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.builderkit.base.ui.commands.processCommand
import com.builderkit.base.ui.components.AppBarScreen
import com.builderkit.features.profile.R as profileR
import com.builderkit.base.R as baseR
import org.koin.androidx.compose.koinViewModel

internal fun NavGraphBuilder.editEmail(
    onBackClicked: () -> Unit,
) {
    composable(AppRoutes.EditEmail.path) {
        val editEmailViewmodel: EditEmailViewModel = koinViewModel()
        val state by editEmailViewmodel.uiState.collectAsStateWithLifecycle()
        val user by editEmailViewmodel.user.collectAsStateWithLifecycle()
        AppBarScreen(
            appBarTitle = stringResource(id = profileR.string.email_address),
            leadingIcon = baseR.drawable.ic_back,
            leadingIconClicked = onBackClicked
        ) {
            EditEmailScreen(
                state = state,
                user = user,
                navigateUp = onBackClicked,
                onCommand = editEmailViewmodel::processCommand,
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

fun NavController.navigateToEditEmail() {
    navigate(AppRoutes.EditEmail.path)
}