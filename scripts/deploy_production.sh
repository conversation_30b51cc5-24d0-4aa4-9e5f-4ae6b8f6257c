#!/bin/bash

# Exit on error
set -e

# Print commands for debugging
set -x

echo "Starting production deployment process..."

# Navigate to the project root directory
# Use this if the script might be called from anywhere
cd "$(dirname "$0")/.." || exit 1

# Check if distributer.json exists
if [ ! -f "./distributer.json" ]; then
  echo "Error: distributer.json not found in the project root directory."
  echo "Please ensure the Google Play JSON key file is available before running this script."
  exit 1
fi

# Run fastlane deploy_production lane with JSON key
echo "Running fastlane deploy to Google Play Store..."
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/distributer.json"

# Ensure we're in the root directory for fastlane execution
echo -e "${CYAN}Executing fastlane from $(pwd)...${NC}"

fastlane android deploy_production

echo "Production deployment process completed successfully!"