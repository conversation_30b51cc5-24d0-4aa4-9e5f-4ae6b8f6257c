#!/bin/bash

# Define color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if 'saas' remote exists
if ! git remote | grep -q "^saas$"; then
    echo -e "${RED}❌ Error: 'saas' remote not found${NC}"
    echo -e "${YELLOW}💡 Please add the saas remote using: git remote add saas <repository-url>${NC}"
    exit 1
fi

# Fetch the latest changes from saas remote
echo -e "${BLUE}🔄 Fetching latest changes from saas remote...${NC}"
if ! git fetch saas main; then
    echo -e "${RED}❌ Error: Failed to fetch from saas remote${NC}"
    exit 1
fi

# Get current branch name
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Check if we're on main branch, if not switch to it
if [ "$CURRENT_BRANCH" != "main" ]; then
    echo -e "${YELLOW}🔀 Switching to main branch...${NC}"
    if ! git checkout main; then
        echo -e "${RED}❌ Error: Failed to switch to main branch${NC}"
        exit 1
    fi
fi

# Merge saas/main into current main branch
echo -e "${BLUE}🔄 Merging saas/main into main branch...${NC}"
if ! git merge saas/main --no-edit; then
    echo -e "${RED}❌ Error: Merge conflicts detected. Please resolve conflicts manually${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Successfully synchronized with saas repository${NC}"