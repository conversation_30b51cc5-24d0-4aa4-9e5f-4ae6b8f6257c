package com.builderkit.base.ui.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors

/**
 * IconPosition enum defines the position of the icon relative to the text
 */
enum class IconPosition {
    START, END
}

/**
 * TextWithIcon is a composable that displays text with an optional icon at the start or end.
 *
 * @param text The text to be displayed
 * @param modifier Modifier to be applied to the component
 * @param color Text color
 * @param fontSize Text font size
 * @param fontStyle Text font style
 * @param fontWeight Text font weight
 * @param fontFamily Text font family
 * @param letterSpacing Text letter spacing
 * @param textDecoration Text decoration
 * @param textAlign Text alignment
 * @param lineHeight Text line height
 * @param overflow Text overflow handling
 * @param softWrap Whether the text should break at soft line breaks
 * @param maxLines Maximum number of lines for the text
 * @param onTextLayout Callback that is executed when the text layout is available
 * @param style Text style
 * @param iconPosition Position of the icon relative to the text (START or END)
 * @param iconSpacing Spacing between the icon and text in dp
 * @param contentDescription Content description for the icon for accessibility
 * @param icon Resource ID of the icon to be displayed
 * @param onClick Optional click handler for the component
 */
@Composable
fun TextWithIcon(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
    iconPosition: IconPosition = IconPosition.END,
    iconSpacing: Int = 8,
    contentDescription: String? = null,
    @DrawableRes icon: Int? = null,
    onClick: (() -> Unit)? = null,
) {
    val rowModifier = if (onClick != null) {
        modifier.clickable(onClick = onClick)
    } else {
        modifier
    }

    Row(verticalAlignment = Alignment.CenterVertically, modifier = rowModifier) {
        if (iconPosition == IconPosition.START && icon != null) {
            Image(
                painter = painterResource(id = icon),
                contentDescription = contentDescription,
            )
            Spacer(modifier = Modifier.width(iconSpacing.dp))
        }

        Text(
            text = text,
            color = color,
            fontSize = fontSize,
            fontStyle = fontStyle,
            fontWeight = fontWeight,
            fontFamily = fontFamily,
            letterSpacing = letterSpacing,
            textDecoration = textDecoration,
            textAlign = textAlign,
            lineHeight = lineHeight,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            onTextLayout = onTextLayout,
            style = style,
        )

        if (iconPosition == IconPosition.END && icon != null) {
            Spacer(modifier = Modifier.width(iconSpacing.dp))
            Image(
                painter = painterResource(id = icon),
                contentDescription = contentDescription,
            )
        }
    }
}

/**
 * TextDrawableEnd is a composable that displays text with an optional icon at the end.
 * This is a convenience wrapper around TextWithIcon with iconPosition set to END.
 */
@Composable
fun TextDrawableEnd(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
    padding: Int = 0,
    contextDescription: String? = null,
    @DrawableRes icon: Int? = null,
    onClick: (() -> Unit)? = null,
) {
    TextWithIcon(
        text = text,
        modifier = modifier,
        color = color,
        fontSize = fontSize,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        letterSpacing = letterSpacing,
        textDecoration = textDecoration,
        textAlign = textAlign,
        lineHeight = lineHeight,
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        onTextLayout = onTextLayout,
        style = style,
        iconPosition = IconPosition.END,
        iconSpacing = padding,
        contentDescription = contextDescription,
        icon = icon,
        onClick = onClick,
    )
}

/**
 * TextDrawableStart is a composable that displays text with an optional icon at the start.
 * This is a convenience wrapper around TextWithIcon with iconPosition set to START.
 */
@Composable
fun TextDrawableStart(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
    padding: Int = 0,
    contextDescription: String? = null,
    @DrawableRes icon: Int? = null,
    onClick: (() -> Unit)? = null,
) {
    TextWithIcon(
        text = text,
        modifier = modifier,
        color = color,
        fontSize = fontSize,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        letterSpacing = letterSpacing,
        textDecoration = textDecoration,
        textAlign = textAlign,
        lineHeight = lineHeight,
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        onTextLayout = onTextLayout,
        style = style,
        iconPosition = IconPosition.START,
        iconSpacing = padding,
        contentDescription = contextDescription,
        icon = icon,
        onClick = onClick,
    )
}

@Preview(showBackground = true)
@Composable
private fun TextDrawableEndPreview() {
    BuilderKitTheme {
        TextDrawableEnd(
            text = "Hello",
            icon = android.R.drawable.arrow_down_float,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TextDrawableStartPreview() {
    BuilderKitTheme {
        TextDrawableStart(
            text = "Hello",
            icon = android.R.drawable.arrow_down_float,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TextWithIconPreview() {
    BuilderKitTheme {
        TextWithIcon(
            text = "Click Me",
            icon = android.R.drawable.ic_dialog_info,
            iconPosition = IconPosition.START,
            style = MaterialTheme.typography.bodyMedium.copy(
                color = LocalColors.current.primary
            ),
            onClick = {}
        )
    }
}
