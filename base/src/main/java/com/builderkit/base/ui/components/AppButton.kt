package com.builderkit.base.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.progressSemantics
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.builderkit.base.ui.extensions.conditional
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.ButtonHeight
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.White
import com.builderkit.base.utils.toDp

@Composable
fun AppButton(
    contentText: String,
    modifier: Modifier = Modifier,
    buttonColors: ButtonColors = ButtonDefaults.buttonColors(
        containerColor = LocalColors.current.primary,
    ),
    textStyle: TextStyle = MaterialTheme.typography.labelMedium.copy(
        color = White,
    ),
    defaultHeight: Dp? = ButtonHeight,
    contentPadding: PaddingValues = ButtonDefaults.ContentPadding,
    enabled: Boolean = true,
    withPadding: Boolean = true,
    loading: Boolean = false,
    onClick: () -> Unit = {},
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val density = LocalDensity.current
    var height by remember { mutableStateOf(0.dp) }
    Button(
        enabled = enabled,
        onClick = {
            if (loading) return@Button
            onClick()
            keyboardController?.hide()
        },
        modifier = modifier
            .conditional(defaultHeight != null) {
                height(defaultHeight!!)
            }
            .onGloballyPositioned {
                height = it.size.height.toDp(density)
            },
        colors = buttonColors,
        contentPadding = if (withPadding) contentPadding else PaddingValues(0.dp),
        content = {
            if (loading) {
                CircularProgressIndicator(
                    color = textStyle.color,
                    modifier = Modifier
                        .progressSemantics()
                        .size(height / 2),
                )
            } else {
                Text(
                    text = contentText,
                    style = textStyle.copy(color = White),
                )
            }
        },
    )
}

@Composable
fun AppOutlinedButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = ButtonDefaults.ContentPadding,
    style: TextStyle = MaterialTheme.typography.titleMedium.copy(
        fontWeight = FontWeight.SemiBold,
        color = White
    )
) {
    OutlinedButton(
        modifier = modifier,
        onClick = onClick,
        border = BorderStroke(width = 2.dp, color = LocalColors.current.primary),
        contentPadding = contentPadding
    ) {
        Text(
            text = text,
            style = style,
        )
    }
}

@Preview
@Composable
fun AppButtonPreview() {
    BuilderKitTheme {
        AppButton(contentText = "Click Me", modifier = Modifier.fillMaxWidth())
    }
}

@Preview
@Composable
fun AppButtonPreviewDisabled() {
    BuilderKitTheme {
        AppButton(contentText = "Click Me", enabled = false, modifier = Modifier.fillMaxWidth())
    }
}

@Preview
@Composable
fun AppButtonPreviewLoading() {
    BuilderKitTheme {
        AppButton(contentText = "Click Me", loading = true, modifier = Modifier.fillMaxWidth())
    }
}


@Preview
@Composable
fun AppOutlinedButtonPreview() {
    BuilderKitTheme {
        AppOutlinedButton(text = "Click Me", onClick = {}, modifier = Modifier.fillMaxWidth())
    }
}