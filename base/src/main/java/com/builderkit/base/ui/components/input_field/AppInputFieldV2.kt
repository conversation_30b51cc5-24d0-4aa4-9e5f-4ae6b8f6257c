package com.builderkit.base.ui.components.input_field

import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.builderkit.base.R
import com.builderkit.base.ui.components.TextDrawableStart
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.White

@Composable
fun AppInputFieldV2(
    text: String,
    placeholder: String,
    isError: Boolean,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    enabled: Boolean = true,
    singleLine: Boolean = true,
    minLines: Int = 1,
    errorMessage: String? = null,
    label: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val borderColor = when {
        isError -> LocalColors.current.negative
        interactionSource.collectIsFocusedAsState().value -> LocalColors.current.accent
        else -> LocalColors.current.borderLine
    }
    Column(modifier = modifier) {
        label?.invoke()
        TextField(
            modifier = Modifier
                .fillMaxWidth()
                .border(
                    width = 1.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(12.dp)
                ),
            interactionSource = interactionSource,
            value = text,
            onValueChange = onValueChange,
            singleLine = singleLine,
            placeholder = {
                Text(
                    text = placeholder,
                    style = MaterialTheme.typography.bodySmall,
                    color = LocalColors.current.body,
                )
            },
            minLines = minLines,
            keyboardOptions = keyboardOptions,
            shape = RoundedCornerShape(12.dp),
            colors = TextFieldDefaults.colors(
                focusedContainerColor = White,
                unfocusedContainerColor = White,
                errorContainerColor = LocalColors.current.negative,
                unfocusedIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
                disabledContainerColor = White,
                errorIndicatorColor = Color.Transparent,
                cursorColor = LocalColors.current.accent,
                errorCursorColor = LocalColors.current.accent,
            ),
            enabled = enabled,
            isError = isError,
            textStyle = MaterialTheme.typography.bodyMedium.copy(color = LocalColors.current.body),
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon
        )
        if (errorMessage.isNullOrEmpty().not()) {
            Spacer(modifier = Modifier.height(5.dp))
            TextDrawableStart(
                text = errorMessage!!,
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color.Red,
                ),
                icon = R.drawable.ic_error,
                modifier = Modifier.fillMaxWidth(),
                padding = 4,
            )
        }
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun AppInputFieldV2Preview() {
    BuilderKitTheme {
        Row(modifier = Modifier.padding(10.dp)) {
            AppInputFieldV2(
                text = "",
                placeholder = "Placeholder",
                isError = false,
                label = null,
                onValueChange = {},
            )
        }

    }
}