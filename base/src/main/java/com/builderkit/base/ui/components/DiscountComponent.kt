package com.builderkit.base.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.builderkit.base.R
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.LocalColors
import com.builderkit.base.ui.theme.White

@Composable
fun DiscountComponent(discountValue: String, modifier: Modifier = Modifier) {
    Text(
        text = "$discountValue% ${stringResource(id = R.string.percentage_off)}",
        style = MaterialTheme.typography.labelSmall.copy(
            fontWeight = FontWeight.SemiBold,
            fontSize = 12.sp,
            color = White,
        ),
        modifier = modifier
            .background(
                color = LocalColors.current.negative,
                shape = RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 10.dp),
            )
            .padding(horizontal = 8.dp, vertical = 3.dp),
        textAlign = TextAlign.Center,
    )
}

@Preview
@Composable
internal fun DiscountComponentPreview() {
    BuilderKitTheme {
        DiscountComponent(discountValue = "20", modifier = Modifier.requiredWidth(100.dp))
    }
}
