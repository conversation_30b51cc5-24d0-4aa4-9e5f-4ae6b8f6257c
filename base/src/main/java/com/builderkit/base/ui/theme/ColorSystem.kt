package com.builderkit.base.ui.theme

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color

private object Colors {
    val COLOR_PRIMARY = Color(0xFF673EB6)
    val COLOR_PRIMARY_100 = Color(0xFFf0ecf8)
    val COLOR_PRIMARY_200 = Color(0xFFD1C5E9)
    val COLOR_PRIMARY_300 = Color(0xFFb39fdb)
    val COLOR_PRIMARY_400 = Color(0xFFa48bd3)
    val COLOR_PRIMARY_500 = Color(0xFF9578cc)
    val COLOR_PRIMARY_600 = Color(0xFF8565c5)
    val COLOR_SECONDARY = Color(0xFFFFFFFF)
    val COLOR_TRANSPARENT = Color.Transparent
    val TEXT = Color(0xFF3B3D3B)
    val BODY = Color(0xFF6E726E)
    val ACCENT = Color(0xFF0CD3FF)
    val BORDER_LINE = Color(0xFFE5E5E5) //gray100
    val NEUTRAL = Color(0xFFC8C8C8) //gray300
    //functional colors
    val POSITIVE = Color(0xFF14BA2D)
    val NEGATIVE = Color(0xFFFA290C)
    val CRITICAL = Color(0xFFFAB70C)
}

@Immutable
abstract class ColorsSystem {
    abstract val primary: Color
    abstract val primary100: Color
    abstract val primary200: Color
    abstract val primary300: Color
    abstract val primary400: Color
    abstract val primary500: Color
    abstract val primary600: Color
    abstract val secondary: Color
    abstract val transparent: Color
    abstract val text: Color
    abstract val body: Color
    abstract val accent: Color
    abstract val borderLine: Color
    abstract val neutral: Color
    abstract val positive: Color
    abstract val negative: Color
    abstract val critical: Color
}

@Immutable
data class LightColors(
    override val primary: Color = Colors.COLOR_PRIMARY,
    override val primary100: Color = Colors.COLOR_PRIMARY_100,
    override val primary200: Color = Colors.COLOR_PRIMARY_200,
    override val primary300: Color = Colors.COLOR_PRIMARY_300,
    override val primary400: Color = Colors.COLOR_PRIMARY_400,
    override val primary500: Color = Colors.COLOR_PRIMARY_500,
    override val primary600: Color = Colors.COLOR_PRIMARY_600,
    override val secondary: Color = Colors.COLOR_SECONDARY,
    override val transparent: Color = Colors.COLOR_TRANSPARENT,
    override val text: Color = Colors.TEXT,
    override val body: Color = Colors.BODY,
    override val accent: Color = Colors.ACCENT,
    override val borderLine: Color = Colors.BORDER_LINE,
    override val neutral: Color = Colors.NEUTRAL,
    override val positive: Color = Colors.POSITIVE,
    override val negative: Color = Colors.NEGATIVE,
    override val critical: Color = Colors.CRITICAL,
) : ColorsSystem()

@Immutable
data class DarkColors(
    override val primary: Color = Colors.COLOR_PRIMARY,
    override val primary100: Color = Colors.COLOR_PRIMARY_100,
    override val primary200: Color = Colors.COLOR_PRIMARY_200,
    override val primary300: Color = Colors.COLOR_PRIMARY_300,
    override val primary400: Color = Colors.COLOR_PRIMARY_400,
    override val primary500: Color = Colors.COLOR_PRIMARY_500,
    override val primary600: Color = Colors.COLOR_PRIMARY_600,
    override val secondary: Color = Colors.COLOR_SECONDARY,
    override val transparent: Color = Colors.COLOR_TRANSPARENT,
    override val text: Color = Colors.TEXT,
    override val body: Color = Colors.BODY,
    override val accent: Color = Colors.ACCENT,
    override val borderLine: Color = Colors.BORDER_LINE,
    override val neutral: Color = Colors.NEUTRAL,
    override val positive: Color = Colors.POSITIVE,
    override val negative: Color = Colors.NEGATIVE,
    override val critical: Color = Colors.CRITICAL,
) : ColorsSystem()

val LocalColors = staticCompositionLocalOf<ColorsSystem> { LightColors() }