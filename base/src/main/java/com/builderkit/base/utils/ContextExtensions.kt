package com.builderkit.base.utils

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.browser.customtabs.CustomTabColorSchemeParams
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.content.ContextCompat
import com.builderkit.base.R
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

fun Context.copyToClipboard(
    label: String,
    message: String
) {
    val clipboardManager = this.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clipData = ClipData.newPlainText(label, message)
    clipboardManager.setPrimaryClip(clipData)
}

fun Context.createFileFromUri(uri: Uri, name: String? = null): File {
    val uriFileDatePattern = "yyyyMMdd_HHmmss"
    val timeStamp = SimpleDateFormat(uriFileDatePattern, Locale.ENGLISH)
        .format(Date())
    val inputStream = contentResolver.openInputStream(uri)
    val file = File(cacheDir, name ?: timeStamp)
    inputStream?.use { input ->
        FileOutputStream(file).use { output ->
            input.copyTo(output)
        }
    }
    return file
}

fun Context.shareLink(
    link: String,
    title: String
) {
    val intent = Intent(Intent.ACTION_SEND)
    intent.type = "text/plain"
    intent.putExtra(Intent.EXTRA_TEXT, link)
    try {
        startActivity(Intent.createChooser(intent, title))
    } catch (_: Exception) {
        Toast.makeText(
            this,
            this.getString(R.string.something_went_wrong),
            Toast.LENGTH_SHORT
        ).show()
    }
}

fun Context.openUrl(url: String) {
    val uri: Uri = Uri.parse(url)
    val intent = Intent(Intent.ACTION_VIEW, uri)
    this.startActivity( Intent.createChooser(intent, null))
}

fun Context.findActivity(): Activity? = when (this) {
    is Activity -> this
    is ContextWrapper -> baseContext.findActivity()
    else -> null
}

fun Context.openRateApp() {
    val uri = Uri.parse("market://details?id=${packageName}")
    val goToMarket = Intent(Intent.ACTION_VIEW, uri)
    goToMarket.addFlags(
        Intent.FLAG_ACTIVITY_NO_HISTORY or
                Intent.FLAG_ACTIVITY_NEW_DOCUMENT or
                Intent.FLAG_ACTIVITY_MULTIPLE_TASK
    )
    try {
        startActivity(goToMarket)
    } catch (e: Exception) {
        startActivity(
            Intent(
                Intent.ACTION_VIEW,
                Uri.parse("https://play.google.com/store/apps/details?id=${packageName}")
            )
        )
    }
}

fun Context.openInternalLink(url: String) {
    val customTabsIntent = CustomTabsIntent.Builder()
        .setShowTitle(true)
        .setShareState(CustomTabsIntent.SHARE_STATE_OFF)
        .setColorSchemeParams(
            CustomTabsIntent.COLOR_SCHEME_LIGHT,
            CustomTabColorSchemeParams.Builder()
                .setToolbarColor(
                    ContextCompat.getColor(
                        this,
                        R.color.primary_color,
                    ),
                ).build(),
        )
        .build()
    customTabsIntent.launchUrl(this, Uri.parse(url))
}