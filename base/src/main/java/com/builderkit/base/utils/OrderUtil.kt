package com.builderkit.base.utils

import android.content.Context
import app.instakit.api.data.models.order.Order
import com.builderkit.base.R

fun Order.OrderStatus.getStatusText(context: Context): String? {
    return when (this) {
        Order.OrderStatus.PENDING -> context.getString(R.string.pending)
        Order.OrderStatus.PREPARING -> context.getString(R.string.preparing)
        Order.OrderStatus.READY -> context.getString(R.string.ready)
        Order.OrderStatus.OUT_FOR_DELIVERY -> context.getString(R.string.delivery)
        Order.OrderStatus.COMPLETED -> context.getString(R.string.completed)
        Order.OrderStatus.CANCELED -> context.getString(R.string.cancelled)
        Order.OrderStatus.PENDING_PAYMENT -> context.getString(R.string.pending_payment)
        Order.OrderStatus.UNKNOWN -> null
    }
}

fun Order.OrderStatus.getOrderStatusImageResIds(): Int {
    return when (this) {
        Order.OrderStatus.PENDING,
        Order.OrderStatus.PENDING_PAYMENT,
        Order.OrderStatus.UNKNOWN -> R.drawable.ic_clock

        Order.OrderStatus.PREPARING -> R.drawable.ic_load
        Order.OrderStatus.READY -> R.drawable.ic_bag_timer
        Order.OrderStatus.OUT_FOR_DELIVERY -> R.drawable.ic_truck_fast
        Order.OrderStatus.COMPLETED -> R.drawable.ic_calendar_tick
        Order.OrderStatus.CANCELED -> R.drawable.ic_calendar_remove
    }
}