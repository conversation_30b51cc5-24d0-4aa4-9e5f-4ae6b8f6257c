package com.builderkit.base.model

import app.instakit.api.data.models.order.Order
import com.builderkit.base.ui.extensions.toDataList
import com.builderkit.base.ui.model.DataList
import kotlinx.serialization.Serializable

@Serializable
data class RateOrderUI(
    val orderId: Long,
    val images: DataList<String>,
    val items: DataList<String>,
    val total: Float,
    val itemsCount: Int,
    val orderServiceType: Order.OrderServiceType
)

val rateOrderUIMock = RateOrderUI(
    orderId = 1,
    items = listOf("Item 1", "Item 2", "Item 3").toDataList(),
    images = listOf("image1", "image2", "image3").toDataList(),
    itemsCount = 3,
    total = 100f,
    orderServiceType = Order.OrderServiceType.DELIVERY
)
