package com.builderkit.base.permissions

interface PermissionsManager {

    fun init()

    fun hasPermissions(): Boolean

    fun requestPermissions(callback: Callback)

    fun shouldShowRationale(): Boolean

    fun shouldShowSettingsDialog(): Boolean

    fun setRationaleShown()

    fun launchSettings()

    interface Callback {
        fun onPermissionGranted()
        fun onPermissionDenied()
        fun onPermissionDeniedPermanently()
    }
}