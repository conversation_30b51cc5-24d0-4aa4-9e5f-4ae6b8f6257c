package com.builderkit.base.viewmdoel

import androidx.compose.runtime.RememberObserver
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner

class CompositionScopedViewModelStoreOwner : ViewModelStoreOwner, RememberObserver {

    override val viewModelStore = ViewModelStore()

    override fun onAbandoned() {
        viewModelStore.clear()
    }

    override fun onForgotten() {
        viewModelStore.clear()
    }

    override fun onRemembered() {
        // Nothing to do here
    }
}
