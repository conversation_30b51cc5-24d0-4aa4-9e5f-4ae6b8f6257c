package com.builderkit.repos.contracts

import app.instakit.api.data.models.product.Product
import app.instakit.api.data.responses.product.ProductsByCategory
import app.instakit.api.utils.ApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow

interface ProductsRepoContract {

    val wishlistActions: SharedFlow<Product>

    fun fetchProductsByCategory(categoryId: Long, page: Int): Flow<ApiResult<List<Product>>>

    fun fetchProductsByCategoryPaginated(
        categoryId: Long,
        page: Int
    ): Flow<ApiResult<ProductsByCategory>>

    fun fetchProductDetails(sku: String): Flow<ApiResult<Product>>

    fun notifyMeWhenAvailable(sku: String): Flow<ApiResult<Product>>

    fun toggleFavorite(sku: String, isFavorite: Boolean): Flow<Result<Product>>
}
