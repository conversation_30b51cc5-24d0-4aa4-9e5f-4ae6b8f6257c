package com.builderkit.repos

import app.instakit.api.data.models.home.Section
import app.instakit.api.data.models.home.SectionContent
import com.builderkit.data.localdatasource.caching.AppServicesCaching
import com.builderkit.datasource.HomeRemoteDatasource
import com.builderkit.datasource.extensions.toResult
import com.builderkit.models.Service
import com.builderkit.repos.contracts.HomeRepoContract
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext

class HomeRepository(
    private val homeRemoteDatasource: HomeRemoteDatasource,
    private val servicesCaching: AppServicesCaching,
) : HomeRepoContract {

    override fun fetchAvailableServices(): Flow<List<Service>> = flow {
        emit(servicesCaching.getServices())
    }

    override suspend fun updateSelectedService(service: Service) = withContext(Dispatchers.IO) {
        servicesCaching.updateService(service)
    }

    override fun fetchHomeCategoriesSection(sectionId: Int): Flow<Result<SectionContent>> = flow {
        val apiResult = homeRemoteDatasource.fetchHomeCategoriesSection(sectionId)
        emit(apiResult.toResult())
    }

    override fun fetchHomeProductsSection(sectionId: Int): Flow<Result<SectionContent>> = flow {
        val apiResult = homeRemoteDatasource.fetchHomeProductsSection(sectionId)
        emit(apiResult.toResult())
    }

    override fun fetchHomeBannersSection(sectionId: Int): Flow<Result<SectionContent>> = flow {
        val apiResult = homeRemoteDatasource.fetchHomeBannersSection(sectionId)
        emit(apiResult.toResult())
    }

    override fun fetchHomeSections(): Flow<Result<List<Section>>> = flow {
        val apiResult = homeRemoteDatasource.fetchHomeSections()
        emit(apiResult.toResult())
    }
}
