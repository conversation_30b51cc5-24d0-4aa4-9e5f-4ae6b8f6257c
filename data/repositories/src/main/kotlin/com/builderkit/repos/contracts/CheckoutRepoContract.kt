package com.builderkit.repos.contracts

import app.instakit.api.data.models.checkout.PickupTimeSlot
import app.instakit.api.data.models.order.Order
import app.instakit.api.data.responses.cart.FetchCartResponse
import app.instakit.api.data.responses.checkout.CheckoutResponse
import app.instakit.api.utils.ApiResult
import com.builderkit.models.Service
import kotlinx.coroutines.flow.Flow

interface CheckoutRepoContract {

    fun fetchAvailablePickupTimeslots(): Flow<ApiResult<List<PickupTimeSlot>>>

    fun fetchCheckoutData(): Flow<ApiResult<CheckoutResponse>>

    fun fetchSelectedServiceId(): Flow<Long>

    fun fetchSelectedServiceName(): Flow<String>

    fun placePickupOrder(
        paymentMethod: String,
        savedCardId: Long?,
        timeSlotId: Long?,
        date: String,
        notes: String,
    ): Flow<Result<Pair<Order, String?>>>

    fun placeDeliveryOrder(
        paymentMethod: String,
        savedCardId: Long?,
        addressId: Long,
        notes: String,
        date: String,
    ): Flow<Result<Pair<Order, String?>>>

    fun fetchSelectedService(): Flow<Service?>

    fun applyPromoCode(promoCode: String): Flow<Result<FetchCartResponse>>

    fun removePromoCode(): Flow<Result<FetchCartResponse>>

    fun useValidPoints(): Flow<Result<FetchCartResponse>>
}
