package com.builderkit.data.caching.impl

import com.builderkit.data.caching.dao.AddressDao
import com.builderkit.data.localdatasource.caching.AddressCaching
import com.builderkit.models.Address

class AddressCaching(private val dao: AddressDao) : AddressCaching {

    override suspend fun saveAddress(address: Address) = dao.saveAddress(address.toEntity())

    override suspend fun getDefaultAddress(): Address? = dao.getDefaultAddress()?.toModel()

    override suspend fun getAddresses(): List<Address> = dao.getAddresses().map { it.toModel() }

    override suspend fun updateAddress(address: Address) {
        val previousSelectedAddress = dao.getSelectedAddress()
        if (previousSelectedAddress != null && previousSelectedAddress.id != address.id) {
            dao.updateAddress(previousSelectedAddress.copy(selected = false))
        }
        dao.updateAddress(address.toEntity())
    }

    override suspend fun saveAddresses(addresses: List<Address>) {
        val selectedAddress = getAddresses().find { it.selected }
        dao.saveAddresses(
            addresses.map {
                val entity = it.toEntity()
                when {
                    selectedAddress != null && selectedAddress.id == entity.id -> entity.copy(
                        selected = true
                    )

                    selectedAddress == null && entity.default -> entity.copy(selected = true)
                    else -> entity
                }
            },
        )
    }

    override suspend fun getSelectedAddress(): Address? = dao.getSelectedAddress()?.toModel()

    override suspend fun getAddress(addressId: Long): Address = dao.getAddress(addressId).toModel()

    override suspend fun deleteAddress(addressId: Long) {
        dao.deleteAddress(addressId)
    }

    override suspend fun deleteAddresses() = dao.deleteAddresses()

    private fun Address.toEntity(): com.builderkit.data.caching.entities.Address =
        com.builderkit.data.caching.entities.Address(
            id = id,
            name = name,
            default = default,
            street = street,
            city = city,
            area = area,
            phone = phone,
            buildingType = buildingType,
            buildingNumber = buildingNumber,
            floorNumber = floorNumber,
            apartmentNumber = apartmentNumber,
            additionalInfo = additionalInfo,
            type = type,
            lat = lat,
            lng = lng,
            selected = selected,
            selectedDistrictId = selectedDistrictId,
            selectedCityId = selectedCityId,
        )

    private fun com.builderkit.data.caching.entities.Address.toModel(): Address =
        Address(
            id = id,
            name = name,
            default = default,
            street = street,
            city = city,
            area = area,
            phone = phone,
            buildingType = buildingType,
            buildingNumber = buildingNumber,
            floorNumber = floorNumber,
            apartmentNumber = apartmentNumber,
            additionalInfo = additionalInfo,
            type = type,
            lat = lat,
            lng = lng,
            selected = selected,
            selectedDistrictId = selectedDistrictId,
            selectedCityId = selectedCityId,
        )
}
