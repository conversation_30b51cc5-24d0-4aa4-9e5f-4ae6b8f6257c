package com.builderkit.data.caching.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.builderkit.data.caching.entities.Address
@Dao
interface AddressDao {

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun saveAddress(address: Address)

    @Query("SELECT * FROM Address WHERE `default` = 1")
    suspend fun getDefaultAddress(): Address?

    @Query("SELECT * FROM Address")
    suspend fun getAddresses(): List<Address>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun updateAddress(address: Address)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveAddresses(addresses: List<Address>)

    @Query("SELECT * FROM ADDRESS WHERE SELECTED = 1")
    suspend fun getSelectedAddress(): Address?

    @Query("SELECT * FROM ADDRESS WHERE id = :addressId")
    suspend fun getAddress(addressId: Long): Address

    @Query("DELETE FROM ADDRESS WHERE id = :addressId")
    suspend fun deleteAddress(addressId: Long)

    @Query("DELETE FROM ADDRESS")
    suspend fun deleteAddresses()
}
