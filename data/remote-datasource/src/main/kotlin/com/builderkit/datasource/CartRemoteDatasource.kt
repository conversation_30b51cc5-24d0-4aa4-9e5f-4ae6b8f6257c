package com.builderkit.datasource

import app.instakit.api.data.requests.cart.AddToCartRequest
import app.instakit.api.gateway.cart.CartAPI

class CartRemoteDatasource(private val cartAPI: CartAPI) {

    suspend fun addToCart(request: AddToCartRequest) = cartAPI.addProductToCart(request)

    suspend fun fetchCart() = cartAPI.fetchCart()

    suspend fun updateCartItemQuantity(cartProductId: Long, quantity: Int) =
        cartAPI.updateProductInCart(cartProductId, quantity)

    suspend fun addAddressToCart(addressId: String) = cartAPI.addAddressToCart(addressId)

    suspend fun applyPromoCode(promoCode: String) = cartAPI.applyPromoCode(promoCode)

    suspend fun removePromoCode() = cartAPI.removePromoCode()

    suspend fun useValidPoints() = cartAPI.useValidPoints()
}
