package com.builderkit.activities.developersettings

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Build
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.builderkit.BuildConfig
import com.builderkit.R
import com.builderkit.activities.developersettings.components.DeveloperSettingField
import com.builderkit.base.ui.components.AppButton
import com.builderkit.base.ui.theme.BuilderKitTheme
import com.builderkit.base.ui.theme.spacing
import com.builderkit.navigation.Navigator
import kotlinx.coroutines.flow.collectLatest
import org.koin.android.scope.AndroidScopeComponent
import org.koin.androidx.compose.koinViewModel
import org.koin.androidx.scope.activityScope
import org.koin.core.scope.Scope
import kotlin.system.exitProcess

class DeveloperSettingsActivity : ComponentActivity(), AndroidScopeComponent {

    override val scope: Scope by activityScope()

    private val navigator: Navigator by scope.inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            BuilderKitTheme {
                DeveloperSettingsScreen(
                    onBackClick = ::finish,
                    onSaveClicked = {
                        exitProcess(0)
                    }
                )
            }
        }
    }
}

@Composable
private fun DeveloperSettingsScreen(
    onBackClick: () -> Unit = {},
    onSaveClicked: () -> Unit = {}
) {
    val viewModel: DeveloperSettingsViewModel = koinViewModel()
    val state by viewModel.state.collectAsStateWithLifecycle()

    LaunchedEffect(key1 = Unit) {
        viewModel.navigationSideEffect.collectLatest { navigate ->
            if (navigate) {
                onSaveClicked()
            }
        }
    }
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            DeveloperSettingsTopBar(onBackClick = onBackClick)
        }
    ) { innerPadding ->
        DeveloperSettingsContent(
            modifier = Modifier.padding(innerPadding),
            data = state,
            onSaveClick = { newUrl ->
                viewModel.clearRequiredData()
                viewModel.saveSettings(
                    newBaseUrl = newUrl,
                )
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DeveloperSettingsTopBar(onBackClick: () -> Unit) {
    TopAppBar(
        title = {
            Text(
                text = stringResource(id = R.string.title_activity_developer_settings_activty),
                textAlign = TextAlign.Center,
            )
        },
        navigationIcon = {
            Icon(
                Icons.AutoMirrored.Default.ArrowBack,
                contentDescription = null,
                modifier = Modifier.clickable(onClick = onBackClick),
            )
        }
    )
}

@Composable
private fun DeveloperSettingsContent(
    data: DeveloperSettingsUIData?,
    modifier: Modifier = Modifier,
    onSaveClick: (newUrl: String) -> Unit
) {
    var newBaseUrl by remember { mutableStateOf("") }

    Column(
        modifier = modifier.fillMaxSize(),
    ) {
        DeveloperSettingsList(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f),
            currentBaseUrl = data?.baseUrl?.ifEmpty { BuildConfig.BASE_URL }
                ?: BuildConfig.BASE_URL,
            onBaseUrlChange = { newBaseUrl = it }
        )
        SaveButton(
            modifier = Modifier
                .fillMaxWidth(fraction = 0.8f)
                .align(Alignment.CenterHorizontally),
            onClick = {
                onSaveClick(newBaseUrl)
            },
        )
        Spacer(Modifier.height(MaterialTheme.spacing.medium))
    }
}

@Composable
private fun DeveloperSettingsList(
    modifier: Modifier = Modifier,
    currentBaseUrl: String,
    onBaseUrlChange: (String) -> Unit
) {
    LazyColumn(
        contentPadding = PaddingValues(MaterialTheme.spacing.medium),
        modifier = modifier
    ) {
        item {
            DeveloperSettingField(
                value = currentBaseUrl,
                label = stringResource(id = R.string.change_base_url),
                hint = stringResource(id = R.string.change_base_url_hint),
                leadingIcon = {
                    Icon(Icons.Filled.Build, contentDescription = null)
                },
                modifier = Modifier.fillMaxWidth(),
                finalValue = onBaseUrlChange
            )
        }
    }
}

@Composable
private fun SaveButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    AppButton(
        contentText = stringResource(id = R.string.save_settings),
        onClick = onClick,
        modifier = modifier
    )
}

@Preview
@Composable
private fun DeveloperSettingsScreenPreview() {
    BuilderKitTheme {
        DeveloperSettingsContent(
            data = DeveloperSettingsUIData(baseUrl = "https://www.builderkit.com"),
            onSaveClick = { }
        )
    }
}