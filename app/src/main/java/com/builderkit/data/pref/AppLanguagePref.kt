package com.builderkit.data.pref

import android.content.Context
import androidx.core.content.edit
import com.builderkit.data.localdatasource.pref.AppPreferences
import com.builderkit.models.Locale

class AppLanguagePref(private val context: Context) : AppPreferences.AppLanguage {

    private val preferences = context.getSharedPreferences(
        AppPreferences.AppLanguage.Keys.APP_LANGUAGE_PREF,
        Context.MODE_PRIVATE
    )

    override fun setAppLocale(locale: Locale) {
        preferences.edit {
            putString(AppPreferences.AppLanguage.Keys.APP_LANGUAGE, locale.value)
        }
    }

    override fun getAppLocale(): Locale =
        preferences.getString(AppPreferences.AppLanguage.Keys.APP_LANGUAGE, null)
            ?.let { Locale.fromValue(it) }
            ?: Locale.ENGLISH
}