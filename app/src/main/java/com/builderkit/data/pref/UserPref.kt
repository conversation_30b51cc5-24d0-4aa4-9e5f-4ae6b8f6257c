package com.builderkit.data.pref

import android.content.Context
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.builderkit.data.localdatasource.pref.AppPreferences
import com.builderkit.di.dataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class UserPref(private val context: Context) : AppPreferences.User {
    private val USERKEY = stringPreferencesKey(AppPreferences.User.Keys.USER)

    override fun isUserAvailable(): Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences.contains(USERKEY)
    }

    override fun getUser(): Flow<String?> = context.dataStore.data.map { preferences ->
        preferences[USERKEY]
    }

    override suspend fun saveUser(user: String) {
        context.dataStore.edit { preferences ->
            preferences[USERKEY] = user
        }
    }

    override suspend fun clearUser() {
        context.dataStore.edit { preferences ->
            preferences.remove(USERKEY)
        }
    }
}
