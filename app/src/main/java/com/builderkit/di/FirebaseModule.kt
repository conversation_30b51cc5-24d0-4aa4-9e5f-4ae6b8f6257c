package com.builderkit.di

import com.builderkit.base.analytics.AnalyticsTracker
import com.builderkit.base.analytics.FirebaseAnalyticsTracker
import com.builderkit.models.FirebaseTopicsContract
import com.builderkit.notifications.FirebaseTopicsService
import com.google.firebase.messaging.FirebaseMessaging
import org.koin.dsl.module

val firebaseModule = module {
    factory<FirebaseTopicsContract> {
        FirebaseTopicsService(FirebaseMessaging.getInstance())
    }
}