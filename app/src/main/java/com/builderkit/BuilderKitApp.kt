package com.builderkit

import android.app.Application
import android.content.Context
import app.instakit.di.apisModule
import com.builderkit.base.setLocale
import com.builderkit.base.updateLocale
import com.builderkit.data.pref.AppLanguagePref
import com.builderkit.datasource.di.remoteDatasourceModule
import com.builderkit.di.analyticsModule
import com.builderkit.di.cachingModule
import com.builderkit.di.dispatchersModule
import com.builderkit.di.firebaseModule
import com.builderkit.di.miscellaneousModule
import com.builderkit.di.navigatorModule
import com.builderkit.di.networkModules
import com.builderkit.di.prefsModule
import com.builderkit.di.viewmodelsModule
import com.builderkit.features.addressbook.di.addressBookListingModule
import com.builderkit.features.authentication.di.authModule
import com.builderkit.features.cart.di.cartModule
import com.builderkit.features.checkout.di.checkoutModule
import com.builderkit.features.home.di.homeModule
import com.builderkit.features.location.di.locationModule
import com.builderkit.features.location.places.GooglePlacesSearchClient
import com.builderkit.features.loyalty.di.loyaltyModule
import com.builderkit.features.notifications.di.notificationsModule
import com.builderkit.features.onboarding.di.onboardingModule
import com.builderkit.features.order.details.di.orderDetailsModule
import com.builderkit.features.order.listing.di.ordersListingModule
import com.builderkit.features.order.rate.di.rateOrderModule
import com.builderkit.features.product.details.di.productDetailsModule
import com.builderkit.features.product.di.productsModule
import com.builderkit.features.profile.di.profileModule
import com.builderkit.features.search.di.searchModule
import com.builderkit.log.AppLogger
import com.builderkit.notifications.PushNotificationService
import com.builderkit.repos.di.reposModule
import com.posthog.android.PostHogAndroid
import com.posthog.android.PostHogAndroidConfig
import org.koin.android.ext.koin.androidContext
import org.koin.android.logger.AndroidLogger
import org.koin.core.context.startKoin
import timber.log.Timber

class BuilderKitApp : Application() {

    override fun onCreate() {
        super.onCreate()
        Timber.plant(AppLogger(isDebug = BuildConfig.DEBUG))
        GooglePlacesSearchClient.initialize(applicationContext, BuildConfig.MAP_API_KEY)
        startKoin {
            logger(AndroidLogger())
            androidContext(this@BuilderKitApp)
            modules(
                networkModules,
                remoteDatasourceModule,
                reposModule,
                homeModule,
                viewmodelsModule,
                navigatorModule,
                locationModule,
                productsModule,
                apisModule,
                productDetailsModule,
                prefsModule,
                cartModule,
                cachingModule,
                checkoutModule,
                authModule,
                profileModule,
                ordersListingModule,
                rateOrderModule,
                addressBookListingModule,
                searchModule,
                orderDetailsModule,
                notificationsModule,
                loyaltyModule,
                firebaseModule,
                dispatchersModule,
                onboardingModule,
                miscellaneousModule,
                analyticsModule,
            )
        }
        setAppLanguage()
        PushNotificationService.createNotificationChannel(context = this)
        initPostHog()
    }

    override fun attachBaseContext(base: Context) {
        val localePref = AppLanguagePref(base)
        val currentAppLanguage = localePref.getAppLocale()
        val newContext = updateLocale(base, currentAppLanguage.value)
        super.attachBaseContext(newContext)
    }

    private fun setAppLanguage() {
        val localePref = AppLanguagePref(this)
        val currentAppLanguage = localePref.getAppLocale()
        setLocale(this@BuilderKitApp, currentAppLanguage.value)
    }

    private fun initPostHog() {
        val postHogApiKey = BuildConfig.POST_HOG_API_KEY
        val postHogHost = BuildConfig.POST_HOG_HOST
        PostHogAndroid.setup(
            this,
            PostHogAndroidConfig(apiKey = postHogApiKey, host = postHogHost),
        )
    }
}
