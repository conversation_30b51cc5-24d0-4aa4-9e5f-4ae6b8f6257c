# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.kts.

# Keep JSON annotations
-keepattributes RuntimeVisibleAnnotations

# Keep Moshi-generated JsonAdapter classes
-keep class com.squareup.moshi.** { *; }

# Keep classes with @Json annotation (if using reflection)
-keep @com.squareup.moshi.Json class *

-keep class app.instakit.api.** { *; }
-keep class app.instakit.adapters.** { *; }

# Keep Firebase classes
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Keep Room database classes
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *

# Keep Kotlin Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# Keep DataStore
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}

# Keep Jetpack Compose
-keep class androidx.compose.** { *; }
-keepclassmembers class androidx.compose.** { *; }
-keep class androidx.lifecycle.viewmodel.compose.** { *; }

# Keep Kotlin Serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt
-keepclassmembers class kotlinx.serialization.json.** {
    *** Companion;
}
-keepclasseswithmembers class kotlinx.serialization.json.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep Koin
-keep class org.koin.** { *; }
-keep class * extends org.koin.core.module.Module { *; }

# Keep Retrofit
-keepattributes Signature
-keepattributes Exceptions
-keep class retrofit2.** { *; }
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# Keep OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Keep Coil
-keep class coil.** { *; }

# Keep ExoPlayer
-keep class com.google.android.exoplayer.** { *; }

# Preserve package names and class names for better crash reports
-keeppackagenames
-keepnames class app.instakit.** { *; }
-keepnames class com.builderkit.** { *; }

# Keep all classes that have native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom exceptions
-keepnames class * extends java.lang.Throwable

# Keep line numbers and source file names for all app classes
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# Keep KSP related classes
-dontwarn com.google.devtools.ksp.**
-dontwarn javax.lang.model.**
-dontwarn javax.tools.**
-dontwarn io.grpc.internal.**